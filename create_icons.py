#!/usr/bin/env python3
"""
Create simple icons for PWA
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, filename):
    """Create a simple icon with the RH logo"""
    # Create a new image with a gradient background
    img = Image.new('RGB', (size, size), '#6366f1')
    draw = ImageDraw.Draw(img)
    
    # Draw a circle background
    margin = size // 8
    draw.ellipse([margin, margin, size-margin, size-margin], fill='#ffffff')
    
    # Draw RH text
    try:
        # Try to use a system font
        font_size = size // 3
        font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", font_size)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    # Calculate text position to center it
    text = "RH"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    draw.text((x, y), text, fill='#6366f1', font=font)
    
    # Save the image
    img.save(f'static/{filename}', 'PNG')
    print(f"Created {filename} ({size}x{size})")

def main():
    """Create icons in different sizes"""
    if not os.path.exists('static'):
        os.makedirs('static')
    
    # Create icons for PWA
    create_icon(192, 'icon-192.png')
    create_icon(512, 'icon-512.png')
    
    print("Icons created successfully!")

if __name__ == "__main__":
    main()
