from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, Form, HTTPException, status, File, UploadFile, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse, RedirectResponse, JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi_cache import FastAPICache
from fastapi_cache.backends.redis import RedisBackend
from fastapi_cache.decorator import cache
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Enum as SaEnum, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship, Session, joinedload
from passlib.context import CryptContext
from email_validator import validate_email, EmailNotValidError
from jose import jwt, JWTError
from datetime import datetime, timedelta
import os
import io
import secrets
import requests
import json
import shutil
import redis.asyncio as redis
import logging
from enum import Enum as PyEnum
from typing import Optional, List
from models import PostDB, UserDB, MentorDB, ChatMessageDB, SavedPostDB
from utils import get_password_hash, save_post_image, catch_phase, get_current_user, get_context, get_cached_number_fact
from media_manager import MediaUploadManager, S3Manager
from dotenv import load_dotenv
from PIL import Image


app = FastAPI(title="RealHonest – Honest Conversations for Visionaries")

# Add compression middleware for better performance
app.add_middleware(GZipMiddleware, minimum_size=1000)

app.mount("/static", StaticFiles(directory="static"), name="static")

# Add cache headers for better performance
@app.middleware("http")
async def add_cache_headers(request: Request, call_next):
    response = await call_next(request)

    # Add cache headers for static files
    if request.url.path.startswith("/static/"):
        response.headers["Cache-Control"] = "public, max-age=31536000"  # 1 year
        response.headers["Expires"] = "Thu, 31 Dec 2025 23:59:59 GMT"

    # Add security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"

    return response

templates = Jinja2Templates(directory="templates")

# Add custom template filters
def datetimeformat(value, format='%B %d, %Y at %I:%M %p'):
    """Format a datetime object for display"""
    if value is None:
        return ""
    return value.strftime(format)

templates.env.filters['datetimeformat'] = datetimeformat

logging.basicConfig(level=logging.INFO)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

manager = MediaUploadManager()

STATIC_URL = os.getenv("STATIC_URL", "/static/")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# === Security Setup ===
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# Hash a password
hashed = pwd_context.hash("mysecretpassword")
print("Hashed:", hashed)

# Verify password
print("Verify:", pwd_context.verify("mysecretpassword", hashed))

# === Database Setup ===
engine = create_engine("sqlite:///./realhonest.db", connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

class MentorCategory(PyEnum):
    RESEARCH = "Research"
    ENTREPRENEURSHIP = "Entrepreneurship"
    EDUCATION = "Education"
    SOCIAL_IMPACT = "Social Impact"

class UserDB(Base):
    __tablename__ = "users"
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(100))
    role = Column(String(20), default="author")
    full_name = Column(String(100), nullable=True)
    bio = Column(Text, nullable=True)
    avatar_url = Column(String(200), default="/static/avatars/default.png")
    github_username = Column(String(50), nullable=True)
    verification_token = Column(String(100), nullable=True)
    email_verified = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    dark_mode = Column(Boolean, default=False)
    posts = relationship("PostDB", back_populates="author")
    mentor_profile = relationship("MentorDB", uselist=False, back_populates="user")

class MentorDB(Base):
    __tablename__ = "mentors"
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey("users.id"), unique=True)
    full_name = Column(String(100))
    category = Column(SaEnum(MentorCategory))
    expertise = Column(String(255))
    bio = Column(Text)
    qualifications = Column(Text)
    experience_years = Column(Integer)
    hourly_rate = Column(String(50))
    available = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    user = relationship("UserDB", back_populates="mentor_profile")

class PostDB(Base):
    __tablename__ = "posts"
    id = Column(Integer, primary_key=True)
    title = Column(String(200))
    content = Column(Text)
    image_url = Column(String(200), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    author_id = Column(Integer, ForeignKey("users.id"))
    author = relationship("UserDB", back_populates="posts")

class CommentDB(Base):
    __tablename__ = "comments"
    id = Column(Integer, primary_key=True)
    content = Column(Text)
    author_id = Column(Integer, ForeignKey("users.id"))
    post_id = Column(Integer, ForeignKey("posts.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    author = relationship("UserDB")
    post = relationship("PostDB")

class ActivityDB(Base):
    __tablename__ = "activity"
    id = Column(Integer, primary_key=True)
    action = Column(String(255))
    user_id = Column(Integer, ForeignKey("users.id"))
    timestamp = Column(DateTime, default=datetime.utcnow)
    user = relationship("UserDB")

Base.metadata.create_all(bind=engine)

NUMBER_FACT_CACHE = {}

def get_cached_number_fact(number: int = None):
    if number in NUMBER_FACT_CACHE:
        return NUMBER_FACT_CACHE[number]
    if not number:
        number = "random"
    try:
        # Use HTTPS instead of HTTP for security
        res = requests.get(f"https://numbersapi.com/{number}/trivia", timeout=5)
        fact = res.text
        NUMBER_FACT_CACHE[number] = fact
        return fact
    except Exception:
        return "There's always something new to learn."
    
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_password_hash(password):
    return pwd_context.hash(password)

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

def decode_access_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload.get("sub")  # This is the username
    except JWTError:
        return None

def get_current_user(request: Request, db: Session):
    token = request.cookies.get("access_token", "")
    if not token.startswith("Bearer "):
        return None
    username = decode_access_token(token[len("Bearer "):])
    if not username:
        return None
    return db.query(UserDB).filter(UserDB.username == username).first()

def get_context(page_type: str, user=None):
    contexts = {
        "index": {"type": "index", "description": "Welcome to RealHonest – Honest conversations for visionary minds"},
        "dashboard": {"type": "dashboard", "description": "Your personal space to share insights and connect"},
        "profile": {"type": "profile", "description": "Manage your profile and preferences"},
        "create_post": {"type": "post", "description": "Share something meaningful with the community"}
    }
    base = contexts.get(page_type, {"type": "default"})
    if user:
        base["user"] = {
            "username": user.username,
            "role": user.role,
            "full_name": user.full_name,
            "avatar_url": user.avatar_url
        }
    return base

def validate_image(self, file: UploadFile):
    if file.content_type not in self.allowed_types:
        raise ValueError(f"Unsupported image format: {file.content_type}. Supported types: {', '.join(self.allowed_types)}")

    file.file.seek(0, os.SEEK_END)
    file_size = file.file.tell()
    file.file.seek(0)

    if file_size > self.max_size_mb * 1024 * 1024:
        raise ValueError(f"File too large. Max size is {self.max_size_mb}MB")

    return True

def resize_image(file: UploadFile, max_size=(800, 800)):
    try:
        img_data = file.file.read()
        img = Image.open(io.BytesIO(img_data))
        if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
            img.thumbnail(max_size)
        output = io.BytesIO()
        img.save(output, format=img.format, optimize=True, quality=85)
        output.seek(0)
        file.file.seek(0)  # Reset file pointer
        return output
    except Exception as e:
        raise ValueError(f"Invalid image file: {str(e)}")

def get_all_posts(db: Session):
    return db.query(PostDB).options(joinedload(PostDB.author)).order_by(PostDB.created_at.desc()).all()

@app.on_event("startup")
async def startup_event():
    Base.metadata.create_all(bind=engine)

@app.on_event("startup")
async def startup():
    try:
        redis_client = redis.from_url("redis://localhost", encoding="utf8", decode_responses=True)
        FastAPICache.init(RedisBackend(redis_client), prefix="fastapi-cache")
        logging.info("Redis cache initialized successfully")
    except Exception as e:
        logging.warning(f"Redis cache initialization failed: {e}. Running without cache.")

# === Routes ===
@app.get("/", response_class=HTMLResponse)
async def home(request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    posts = db.query(PostDB).options(joinedload(PostDB.author)).order_by(PostDB.created_at.desc()).all()
    logging.info(f"Fetched {len(posts)} posts for Home page")

    # Add save status and comments count to posts
    posts_with_stats = []
    for post in posts:
        # Check if current user saved this post
        user_saved = False
        if user:
            user_saved = bool(db.execute(
                text("SELECT 1 FROM saved_posts WHERE user_id = :user_id AND post_id = :post_id"),
                {"user_id": user.id, "post_id": post.id}
            ).fetchone())

        # Get comments count
        comments_count = db.execute(
            text("SELECT COUNT(*) as count FROM comments WHERE post_id = :post_id"),
            {"post_id": post.id}
        ).fetchone().count

        # Add stats to post object
        post.user_saved = user_saved
        post.comments_count = comments_count
        posts_with_stats.append(post)

    now = datetime.now()
    context = get_context("index", user=user)
    fact = get_cached_number_fact()
    return templates.TemplateResponse("index.html", {
        "request": request,
        "user": user,
        "posts": posts_with_stats,
        "context": context,
        "now": now,
        "fact": fact
    })

@app.get("/login", response_class=HTMLResponse)
async def login_form(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@app.post("/login")
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    user = db.query(UserDB).filter(UserDB.username == username).first()
    if not user or not verify_password(password, user.hashed_password):
        raise HTTPException(status_code=400, detail="Invalid credentials")
    
    token = create_access_token(data={"sub": user.username})
    response = RedirectResponse(url="/dashboard", status_code=status.HTTP_303_SEE_OTHER)
    response.set_cookie(key="access_token", value=f"Bearer {token}", httponly=True, secure=False, samesite="lax")
    return response

@app.get("/logout")
async def logout():
    response = RedirectResponse(url="/")
    response.delete_cookie("access_token")
    return response

@app.get("/signup", response_class=HTMLResponse)
async def signup_form(request: Request):
    return templates.TemplateResponse("signup.html", {"request": request})

@app.post("/signup")
async def signup(
    request: Request,
    username: str = Form(...),
    email: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    try:
        valid_email = validate_email(email)
        email = valid_email.email
    except EmailNotValidError as e:
        raise HTTPException(status_code=400, detail=f"Invalid email: {str(e)}")

    if db.query(UserDB).filter(UserDB.username == username).first():
        raise HTTPException(status_code=400, detail="Username already taken")

    new_user = UserDB(
        username=username,
        email=email,
        hashed_password=get_password_hash(password),
        role="author",
        verification_token=secrets.token_urlsafe(32),
        avatar_url="/static/avatars/default.png",
        email_verified=True
    )
    db.add(new_user)
    db.commit()
    db.refresh(new_user)

    token = create_access_token(data={"sub": new_user.username})
    response = RedirectResponse(url="/profile", status_code=status.HTTP_303_SEE_OTHER)
    response.set_cookie(key="access_token", value=f"Bearer {token}", httponly=True, secure=False, samesite="lax")
    return response
 
@app.get("/profile", response_class=HTMLResponse)
async def profile_form(request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login")
    return templates.TemplateResponse("profile.html", {"request": request, "user": user})

@app.get("/create-profile", response_class=HTMLResponse)
async def create_profile_form(request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login")
    return templates.TemplateResponse("create-profile.html", {"request": request, "user": user})

@app.post("/create-profile")
async def create_profile(
    request: Request,
    full_name: str = Form(...),
    bio: str = Form(...),
    github_username: str = Form(None),
    avatar: UploadFile = File(None),
    db: Session = Depends(get_db)
):
    auth_header = request.cookies.get("access_token", "")
    if not auth_header.startswith("Bearer "):
        return RedirectResponse(url="/login")
    username = decode_access_token(auth_header[len("Bearer "):])
    user = db.query(UserDB).filter(UserDB.username == username).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    user.full_name = full_name
    user.bio = bio
    user.github_username = github_username

    if avatar and avatar.filename:
        filename = f"{username}_{secrets.token_hex(8)}_{avatar.filename}"
        file_path = os.path.join("static", "avatars", filename)
        contents = await avatar.read()
        with open(file_path, "wb") as f:
            f.write(contents)
        user.avatar_url = f"/static/avatars/{filename}"

    db.commit()
    return RedirectResponse(url="/dashboard", status_code=status.HTTP_303_SEE_OTHER)

@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard_route(request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login")

    # Get posts with save status and comments count
    posts = db.query(PostDB).options(joinedload(PostDB.author)).order_by(PostDB.created_at.desc()).all()
    posts_with_stats = []
    for post in posts:
        # Check if current user saved this post
        user_saved = bool(db.execute(
            text("SELECT 1 FROM saved_posts WHERE user_id = :user_id AND post_id = :post_id"),
            {"user_id": user.id, "post_id": post.id}
        ).fetchone())

        # Get comments count
        comments_count = db.execute(
            text("SELECT COUNT(*) as count FROM comments WHERE post_id = :post_id"),
            {"post_id": post.id}
        ).fetchone().count

        # Add stats to post object
        post.user_saved = user_saved
        post.comments_count = comments_count
        posts_with_stats.append(post)

    fact = get_cached_number_fact()
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user,
        "posts": posts_with_stats,
        "fact": fact
    })

@app.get("/saved-posts", response_class=HTMLResponse)
async def saved_posts_page(request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login")

    return templates.TemplateResponse("saved_posts.html", {
        "request": request,
        "user": user
    })

@app.get("/dark-mode-test", response_class=HTMLResponse)
async def dark_mode_test(request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    return templates.TemplateResponse("dark_mode_test.html", {
        "request": request,
        "user": user
    })

@app.get("/register-mentor", response_class=HTMLResponse)
async def register_mentor_form(request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        raise HTTPException(status_code=401, detail="Unauthorized")
    if user.mentor_profile:
        return RedirectResponse(url="/profile")
    return templates.TemplateResponse("register_mentor.html", {"request": request, "user": user})

@app.post("/register-mentor")
async def register_mentor(
    request: Request,
    full_name: str = Form(...),
    category: str = Form(...),
    expertise: str = Form(...),
    bio: str = Form(...),
    qualifications: str = Form(...),
    experience_years: int = Form(...),
    hourly_rate: str = Form(...),
    available: bool = Form(False),
    db: Session = Depends(get_db)
):
    user = get_current_user(request, db)
    if not user:
        raise HTTPException(status_code=401, detail="Unauthorized")

    existing = db.query(MentorDB).filter(MentorDB.user_id == user.id).first()
    if existing:
        raise HTTPException(status_code=400, detail="Already registered as mentor")

    new_mentor = MentorDB(
        user_id=user.id,
        full_name=full_name,
        category=MentorCategory[category.upper()],
        expertise=expertise,
        bio=bio,
        qualifications=qualifications,
        experience_years=experience_years,
        hourly_rate=hourly_rate,
        available=bool(available)
    )

    db.add(new_mentor)
    db.commit()
    db.refresh(new_mentor)
    return RedirectResponse(url="/profile", status_code=status.HTTP_303_SEE_OTHER)

@app.get("/mentors/{mentor_id}", response_class=HTMLResponse)
async def view_mentor(request: Request, mentor_id: int, db: Session = Depends(get_db)):
    mentor = db.query(MentorDB).get(mentor_id)
    if not mentor:
        return templates.TemplateResponse("mentor_profile.html", {"request": request})
    return templates.TemplateResponse("mentor_profile.html", {
        "request": request,
        "mentor": mentor,
        "user": mentor.user
    })

@app.get("/dm/{username}", response_class=HTMLResponse)
async def direct_message(request: Request, username: str, db: Session = Depends(get_db)):
    other_user = db.query(UserDB).filter(UserDB.username == username).first()
    if not other_user:
        return templates.TemplateResponse("direct_message.html", {
            "request": request,
            "error": "User not found"
        })
    return templates.TemplateResponse("direct_message.html", {
        "request": request,
        "other_user": other_user
    })

@app.get("/chat", response_class=HTMLResponse)
async def chat_room(request: Request, db: Session = Depends(get_db)):
    user = get_current_user(request, db)
    if not user:
        return RedirectResponse(url="/login")
    return templates.TemplateResponse("chat.html", {
        "request": request,
        "user": user
    })

@app.get("/api/user")
async def get_api_user(request: Request, db: Session = Depends(get_db)):
    auth_header = request.cookies.get("access_token", "")
    
    if not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing access token")

    token = auth_header[len("Bearer "):]
    username = decode_access_token(token)

    if not username:
        raise HTTPException(status_code=401, detail="Invalid or expired token")

    user = db.query(UserDB).filter(UserDB.username == username).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    return {
        "username": user.username,
        "role": user.role,
        "full_name": user.full_name,
        "email": user.email,
        "github_username": user.github_username,
        "dark_mode": user.dark_mode,
        "mentor_profile": {"id": user.mentor_profile.id} if user.mentor_profile else None
    }
   
@app.get("/api/posts")
async def get_api_posts(request: Request, db: Session = Depends(get_db), page: int = 1, limit: int = 6):
    offset = (page - 1) * limit
    query = db.query(PostDB).options(joinedload(PostDB.author)).order_by(PostDB.created_at.desc()).offset(offset).limit(limit)
    results = query.all()
    posts = query.all()
    logging.info(f"Fetched {len(results)} posts for API")

    # Get current user to check save status
    user = get_current_user(request, db)

    post_data = []
    for post in posts:
        # Check if current user saved this post
        user_saved = False
        if user:
            user_saved = bool(db.execute(
                text("SELECT 1 FROM saved_posts WHERE user_id = :user_id AND post_id = :post_id"),
                {"user_id": user.id, "post_id": post.id}
            ).fetchone())

        # Get comments count
        comments_count = db.execute(
            text("SELECT COUNT(*) as count FROM comments WHERE post_id = :post_id"),
            {"post_id": post.id}
        ).fetchone().count

        post_data.append({
            "id": post.id,
            "title": post.title,
            "content": post.content,
            "image_url": post.image_url,
            "created_at": post.created_at.isoformat(),
            "author_username": post.author.username if post.author else "Deleted User",
            "user_saved": user_saved,
            "comments_count": comments_count
        })

    return post_data

@app.post("/api/posts")
async def create_post(
    request: Request,
    title: str = Form(...),
    content: str = Form(...),
    image: UploadFile = File(None),
    db: Session = Depends(get_db)
):
    try:
        # Get current user
        auth_header = request.cookies.get("access_token", "")
        if not auth_header.startswith("Bearer "):
            raise HTTPException(status_code=401, detail="Missing access token")
        username = decode_access_token(auth_header[len("Bearer "):])
        user = db.query(UserDB).filter(UserDB.username == username).first()
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Save image if provided
        image_url = None
        if image and image.filename:
            resized_stream = resize_image(image)
            filename = f"post_{secrets.token_hex(8)}_{image.filename}"
            file_path = os.path.join("static", "posts", filename)
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(resized_stream, buffer)
            image_url = f"/static/posts/{filename}"

            
        
        print(f"Saving image to {file_path}")
        
        new_activity = ActivityDB(user_id=user.id, action=f"Posted '{title}'")
        
        # Create new post
        new_post = PostDB(
            title=title,
            content=content,
            image_url=image_url,
            author_id=user.id
        )
        db.add(new_post)
        db.add(new_activity)
        db.commit()
        db.refresh(new_post)
        db.refresh(new_activity)
        
        # Clear cache so dashboard updates (if cache is available)
        try:
            await FastAPICache.clear(namespace="fastapi-cache:get_api_posts")
        except Exception:
            pass  # Cache not available
        return {
            "id": new_post.id,
            "title": new_post.title,
            "content": new_post.content,
            "image_url": new_post.image_url,
            "created_at":new_post.created_at.isoformat(),
            "author_username": user.username
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Server error: {str(e)}")
    
@app.get("/api/posts/{post_id}")
async def get_api_post(post_id: int, db: Session = Depends(get_db)):
    post = db.query(PostDB).get(post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    return {
        "id": post.id,
        "title": post.title,
        "content": post.content,
        "image_url": post.image_url,
        "created_at": post.created_at.isoformat(),
        "author_username": post.author.username
    }

@app.get("/posts/{post_id}", response_class=HTMLResponse)
async def view_post(request: Request, post_id: int, db: Session = Depends(get_db)):
    post = db.query(PostDB).options(joinedload(PostDB.author)).filter(PostDB.id == post_id).first()
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")

    user = get_current_user(request, db)

    # Add save status and comments count to post
    if user:
        user_saved = bool(db.execute(
            text("SELECT 1 FROM saved_posts WHERE user_id = :user_id AND post_id = :post_id"),
            {"user_id": user.id, "post_id": post.id}
        ).fetchone())
        post.user_saved = user_saved
    else:
        post.user_saved = False

    # Get comments count
    comments_count = db.execute(
        text("SELECT COUNT(*) as count FROM comments WHERE post_id = :post_id"),
        {"post_id": post.id}
    ).fetchone().count
    post.comments_count = comments_count

    context = get_context("post", user=user)
    return templates.TemplateResponse("post_detail.html", {
        "request": request,
        "post": post,
        "user": user,
        "context": context
    })

@app.get("/posts/{post_id}/edit", response_class=HTMLResponse)
async def edit_post_form(request: Request, post_id: int, db: Session = Depends(get_db)):
    post = db.query(PostDB).get(post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    user = get_current_user(request, db)
    if not user or post.author_id != user.id:
        raise HTTPException(status_code=403, detail="Not authorized")
    return templates.TemplateResponse("edit_post.html", {
        "request": request,
        "post": post,
        "user": user
    })

@app.post("/api/posts/{post_id}")
async def update_post(
    request: Request,
    post_id: int,
    title: str = Form(...),
    content: str = Form(...),
    image: UploadFile = File(None),
    db: Session = Depends(get_db)
):
    auth_header = request.cookies.get("access_token", "")
    if not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing access token")
    username = decode_access_token(auth_header[len("Bearer "):])
    user = db.query(UserDB).filter(UserDB.username == username).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    post = db.query(PostDB).get(post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    if post.author_id != user.id:
        raise HTTPException(status_code=403, detail="Not authorized")

    # Update fields
    post.title = title
    post.content = content

    # Handle image upload
    if image and image.filename:
        resized_stream = resize_image(image)
        filename = f"post_{secrets.token_hex(8)}_{image.filename}"
        file_path = os.path.join("static", "posts", filename)
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(resized_stream, buffer)
        post.image_url = f"/static/posts/{filename}"

    db.commit()
    return {"message": "Post updated"}

@app.post("/posts/{post_id}/edit")
async def update_post(
    request: Request,
    post_id: int,
    title: str = Form(...),
    content: str = Form(...),
    image: UploadFile = File(None),
    db: Session = Depends(get_db)
):
    auth_header = request.cookies.get("access_token", "")
    if not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing access token")
    username = decode_access_token(auth_header[len("Bearer "):])
    current_user = db.query(UserDB).filter(UserDB.username == username).first()
    
    post = db.query(PostDB).get(post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    if post.author_id != current_user.id:
        raise HTTPException(status_code=403, detail="Not authorized")

    # Update fields
    post.title = title
    post.content = content

    # Handle new image upload
    if image and image.filename:
        resized_stream = resize_image(image)
        filename = f"post_{secrets.token_hex(8)}_{image.filename}"
        file_path = os.path.join("static", "posts", filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(resized_stream, buffer)
        post.image_url = f"/static/posts/{filename}"

    db.commit()
    return RedirectResponse(url=f"/posts/{post_id}", status_code=status.HTTP_303_SEE_OTHER)

@app.get("/api/comments/{post_id}")
async def get_comments(post_id: int, db: Session = Depends(get_db), page: int = 1, limit: int = 5):
    offset = (page - 1) * limit
    comments = db.query(CommentDB).filter(CommentDB.post_id == post_id).order_by(CommentDB.created_at.desc()).offset(offset).limit(limit).all()
    return [{
        "id": c.id,
        "content": c.content,
        "author": c.author.username,
        "created_at": c.created_at.isoformat()
    } for c in comments]


@app.post("/api/upload-image")
async def upload_image(
    request: Request,
    image: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload an image for use in rich text editor"""
    try:
        # Validate user is authenticated
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=401, detail="Authentication required")

        # Validate file type
        if not image.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Validate file size (max 5MB)
        contents = await image.read()
        if len(contents) > 5 * 1024 * 1024:  # 5MB
            raise HTTPException(status_code=400, detail="Image too large (max 5MB)")

        # Reset file pointer
        await image.seek(0)

        # Use existing media manager to save the image
        image_url = manager.save_image(image, "editor_images")

        return JSONResponse({
            "success": True,
            "url": image_url,
            "message": "Image uploaded successfully"
        })

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Image upload error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to upload image")



@app.post("/api/posts/{post_id}/save")
async def toggle_save_post(
    request: Request,
    post_id: int,
    db: Session = Depends(get_db)
):
    """Toggle save/unsave for a post"""
    try:
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=401, detail="Authentication required")

        # Check if post exists
        post = db.query(PostDB).filter(PostDB.id == post_id).first()
        if not post:
            raise HTTPException(status_code=404, detail="Post not found")

        # Check if user already saved this post
        existing_save = db.execute(
            text("SELECT * FROM saved_posts WHERE user_id = :user_id AND post_id = :post_id"),
            {"user_id": user.id, "post_id": post_id}
        ).fetchone()

        if existing_save:
            # Unsave the post
            db.execute(
                text("DELETE FROM saved_posts WHERE user_id = :user_id AND post_id = :post_id"),
                {"user_id": user.id, "post_id": post_id}
            )
            saved = False
        else:
            # Save the post
            db.execute(
                text("INSERT INTO saved_posts (user_id, post_id, created_at) VALUES (:user_id, :post_id, :created_at)"),
                {"user_id": user.id, "post_id": post_id, "created_at": datetime.utcnow()}
            )
            saved = True

        db.commit()

        return JSONResponse({
            "success": True,
            "saved": saved
        })

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Save toggle error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to toggle save")


@app.get("/api/posts/{post_id}/stats")
async def get_post_stats(
    request: Request,
    post_id: int,
    db: Session = Depends(get_db)
):
    """Get post statistics (saves, comments count)"""
    try:
        user = get_current_user(request, db)

        # Get comments count
        comments_count = db.execute(
            text("SELECT COUNT(*) as count FROM comments WHERE post_id = :post_id"),
            {"post_id": post_id}
        ).fetchone().count

        # Check if current user saved this post
        user_saved = False

        if user:
            user_saved = bool(db.execute(
                text("SELECT 1 FROM saved_posts WHERE user_id = :user_id AND post_id = :post_id"),
                {"user_id": user.id, "post_id": post_id}
            ).fetchone())

        return JSONResponse({
            "comments_count": comments_count,
            "user_saved": user_saved
        })

    except Exception as e:
        logging.error(f"Get post stats error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get post stats")


@app.get("/api/saved-posts")
async def get_saved_posts(
    request: Request,
    db: Session = Depends(get_db),
    page: int = 1,
    limit: int = 10
):
    """Get user's saved posts"""
    try:
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=401, detail="Authentication required")

        offset = (page - 1) * limit

        # Get saved posts with post details
        saved_posts = db.execute(
            text("""
                SELECT p.id, p.title, p.content, p.image_url, p.created_at, u.username as author_username, sp.created_at as saved_at
                FROM saved_posts sp
                JOIN posts p ON sp.post_id = p.id
                JOIN users u ON p.author_id = u.id
                WHERE sp.user_id = :user_id
                ORDER BY sp.created_at DESC
                LIMIT :limit OFFSET :offset
            """),
            {"user_id": user.id, "limit": limit, "offset": offset}
        ).fetchall()

        return [{
            "id": post.id,
            "title": post.title,
            "content": post.content,
            "image_url": post.image_url,
            "created_at": post.created_at.isoformat() if hasattr(post.created_at, 'isoformat') else str(post.created_at),
            "author_username": post.author_username,
            "saved_at": post.saved_at.isoformat() if hasattr(post.saved_at, 'isoformat') else str(post.saved_at)
        } for post in saved_posts]

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Get saved posts error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get saved posts")


@app.post("/api/user/dark-mode")
async def toggle_dark_mode(
    request: Request,
    db: Session = Depends(get_db)
):
    """Toggle user's dark mode preference"""
    try:
        user = get_current_user(request, db)
        if not user:
            raise HTTPException(status_code=401, detail="Authentication required")

        # Parse JSON body
        body = await request.json()
        dark_mode = body.get("dark_mode", False)

        # Update user's dark mode preference
        user.dark_mode = dark_mode
        db.commit()

        return JSONResponse({
            "success": True,
            "dark_mode": user.dark_mode
        })

    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Dark mode toggle error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update dark mode preference")


@app.get("/api/user/dark-mode")
async def get_dark_mode(
    request: Request,
    db: Session = Depends(get_db)
):
    """Get user's dark mode preference"""
    try:
        user = get_current_user(request, db)
        if not user:
            return JSONResponse({"dark_mode": False})

        return JSONResponse({
            "dark_mode": user.dark_mode
        })

    except Exception as e:
        logging.error(f"Get dark mode error: {str(e)}")
        return JSONResponse({"dark_mode": False})


@app.post("/api/comments/{post_id}")
async def add_comment(
    request: Request,
    post_id: int,
    db: Session = Depends(get_db)
):
    # Parse JSON body
    try:
        body = await request.json()
        content = body.get("content", "").strip()
        if not content:
            raise HTTPException(status_code=400, detail="Content is required")
    except Exception as e:
        raise HTTPException(status_code=400, detail="Invalid JSON body")

    auth_header = request.cookies.get("access_token", "")
    if not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing access token")
    username = decode_access_token(auth_header[len("Bearer "):])
    user = db.query(UserDB).filter(UserDB.username == username).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    new_comment = CommentDB(
        content=content,
        author_id=user.id,
        post_id=post_id
    )
    db.add(new_comment)
    db.commit()
    db.refresh(new_comment)

    return {
        "id": new_comment.id,
        "content": new_comment.content,
        "author": user.username,
        "created_at": new_comment.created_at.isoformat()
    }


@app.get("/api/activity/{username}")
async def get_user_activity(username: str, db: Session = Depends(get_db)):
    user = db.query(UserDB).filter(UserDB.username == username).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    activities = db.query(ActivityDB).filter(ActivityDB.user_id == user.id).order_by(ActivityDB.timestamp.desc()).limit(10).all()
    return [{
        "action": a.action,
        "timestamp": a.timestamp.isoformat()
    } for a in activities]

@app.delete("/api/posts/{post_id}")
async def delete_post(
    request: Request,
    post_id: int,
    db: Session = Depends(get_db)
):
    auth_header = request.cookies.get("access_token", "")
    if not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing access token")
    username = decode_access_token(auth_header[len("Bearer "):])
    if not username:
        raise HTTPException(status_code=401, detail="Invalid token")
    user = db.query(UserDB).filter(UserDB.username == username).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    post = db.query(PostDB).get(post_id)
    if not post:
        raise HTTPException(status_code=404, detail="Post not found")
    if post.author_id != user.id and user.role != 'admin':
        raise HTTPException(status_code=403, detail="Not authorized")

    # Delete image file if exists
    if post.image_url:
        manager.delete_file(post.image_url)

    new_activity = ActivityDB(user_id=user.id, action=f"Deleted post '{post.title}'")

    db.delete(post)
    db.add(new_activity)
    db.commit()
    return {"message": "Post deleted"}

@app.delete("/api/user")
async def delete_user(request: Request, db: Session = Depends(get_db)):
    auth_header = request.cookies.get("access_token", "")
    if not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing access token")
    username = decode_access_token(auth_header[len("Bearer "):])
    user = db.query(UserDB).filter(UserDB.username == username).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    db.delete(user)
    db.commit()

    response = JSONResponse({"message": "Account deleted"})
    response.delete_cookie("access_token")
    return response

@app.get("/api/mentors")
async def get_api_mentors(db: Session = Depends(get_db)):
    return [{
        "id": m.id,
        "full_name": m.full_name,
        "category": m.category.value,
        "expertise": m.expertise,
        "bio": m.bio,
        "qualifications": m.qualifications,
        "experience_years": m.experience_years,
        "hourly_rate": m.hourly_rate,
        "available": m.available
    } for m in db.query(MentorDB).all()]

@app.get("/api/mentors/{mentor_id}")
async def get_api_mentor(mentor_id: int, db: Session = Depends(get_db)):
    mentor = db.query(MentorDB).get(mentor_id)
    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")
    return {
        "id": mentor.id,
        "full_name": mentor.full_name,
        "category": mentor.category.value,
        "expertise": mentor.expertise,
        "bio": mentor.bio,
        "qualifications": mentor.qualifications,
        "experience_years": mentor.experience_years,
        "hourly_rate": mentor.hourly_rate,
        "available": mentor.available,
        "user": {
            "username": mentor.user.username,
            "role": mentor.user.role
        }
    }

@app.websocket("/ws/dm/{username}")
async def websocket_direct_message(websocket: WebSocket, username: str, db: Session = Depends(get_db)):
    token = websocket.headers.get("Authorization", "")
    if not token.startswith("Bearer "):
        await websocket.close(code=4000)
        return
    
    decoded = decode_access_token(token[len("Bearer "):])
    if not decoded:
        await websocket.close(code=4001)
        return

    await connection_manager.connect(websocket, decoded)
    try:
        while True:
            data = await websocket.receive_text()
            await connection_manager.send_personal_message(data, username)
    except WebSocketDisconnect:
        connection_manager.disconnect(decoded)

@app.websocket("/ws/chat")
async def websocket_chat(websocket: WebSocket):
    await websocket.accept()
    ws_manager = ConnectionManager()  # Make sure you're using the correct instance
    while True:
        try:
            data = await websocket.receive_text()
            await ws_manager.broadcast(data)
        except WebSocketDisconnect:
            break


class ConnectionManager:
    def __init__(self):
        self.active_connections = {}

    async def connect(self, websocket: WebSocket, username: str):
        await websocket.accept()
        self.active_connections[username] = websocket

    def disconnect(self, username: str):
        if username in self.active_connections:
            del self.active_connections[username]

    async def send_personal_message(self, message: str, username: str):
        if username in self.active_connections:
            await self.active_connections[username].send_text(message)

    async def broadcast(self, message: str):
        for connection in self.active_connections.values():
            await connection.send_text(message)

connection_manager = ConnectionManager()

# === Run App ===
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)