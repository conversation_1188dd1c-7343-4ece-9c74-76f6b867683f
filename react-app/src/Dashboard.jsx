import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NewPostForm from './NewPostForm';
import ShareButton from './ShareButton';

export default function Dashboard({ user }) {
  const [github, setGithub] = useState(null);
  const [posts, setPosts] = useState([]);
  const [fact, setFact] = useState("Loading fact...");
  const [showPostForm, setShowPostForm] = useState(false);
  const navigate = useNavigate();

  // Fetch GitHub info if user has username
  useEffect(() => {
    if (user?.github_username) {
      fetch(`https://api.github.com/users/${user.github_username}`)
        .then(res => res.json())
        .then(data => setGithub({
          followers: data.followers,
          public_repos: data.public_repos,
          profile_url: data.html_url
        }))
        .catch(err => console.error("Failed to fetch GitHub data", err));
    }
  }, [user]);

  // Function to fetch posts
  const fetchPosts = () => {
    fetch('/api/posts')
      .then(res => res.json())
      .then(data => setPosts(data))
      .catch(err => console.error(err));
  };

  // Fetch recent community posts on component mount
  useEffect(() => {
    fetchPosts();
  }, []);

  // Listen for post updates
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'postsUpdated') {
        fetchPosts();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    const handleVisibilityChange = () => {
      if (!document.hidden && localStorage.getItem('postsUpdated')) {
        fetchPosts();
        localStorage.removeItem('postsUpdated');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Fetch number fact
  useEffect(() => {
    fetch('https://numbersapi.com/random/trivia')
      .then(res => res.text())
      .then(setFact)
      .catch(err => {
        console.error("Failed to load number fact", err);
        setFact("There's always something new to learn.");
      });
  }, []);





  const handleDelete = async (id) => {
  const confirmed = window.confirm("Are you sure you want to delete this post?");
  if (!confirmed) return;

  try {
    const res = await fetch(`/api/posts/${id}`, {
      method: "DELETE",
      credentials: "include"
    });

    if (!res.ok) {
      const data = await res.json().catch(() => ({ detail: "Failed to delete post" }));
      throw new Error(data.detail || "Something went wrong");
    }

    // Refresh list
    fetchPosts();
  } catch (err) {
    alert(err.message);
  }
};


  if (!user || user.role !== "author") {
    return (
      <div className="text-center py-10">
        <p className="text-red-500 mb-4">Only authors can access dashboard</p>
        <a href="/login" className="text-indigo-600 underline">Go to Login</a>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-6 rounded-lg shadow-lg mb-8">
        <h1 className="text-2xl font-extrabold">Welcome back, {user.full_name || user.username}</h1>
        <p className="mt-2 opacity-90">Share insights, connect, and build honest conversations.</p>
      </section>

      {/* Mentor CTA */}
      {!user.mentor_profile && (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-8 transition-colors duration-300">
          <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Want to Become a Mentor?</h2>
          <p className="mb-4 text-gray-700 dark:text-gray-300">Help others grow by offering guidance in your field.</p>
          <a
            href="/register-mentor"
            className="px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105"
            aria-label="Register as a mentor to help others grow"
          >
            🎓 Register as Mentor
          </a>
        </div>
      )}

      {/* GitHub Info */}
      {user?.github_username && github && (
        <div className="mt-6 bg-white dark:bg-gray-800 p-4 rounded shadow-sm transition-colors duration-300">
          <h2 className="font-semibold mb-2 text-gray-900 dark:text-gray-100">GitHub Profile</h2>
          <p className="text-gray-700 dark:text-gray-300">Followers: {github.followers}</p>
          <p className="text-gray-700 dark:text-gray-300">Repositories: {github.public_repos}</p>
          <p>
            <a
              href={github.profile_url}
              target="_blank"
              rel="noopener noreferrer"
              className="text-indigo-600 dark:text-indigo-400 underline"
            >
              View on GitHub
            </a>
          </p>
        </div>
      )}

      {/* New Post Button */}
      <button
        onClick={() => setShowPostForm(!showPostForm)}
        type="button"
        className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-md transition mb-6"
        aria-label="Create a new post"
      >
        ✏️ New Post
      </button>

      {/* Hidden Post Form */}
      {showPostForm && <NewPostForm onPostCreated={() => {
        fetchPosts(); // Refresh posts list
        setShowPostForm(false); // Hide form after creation
        localStorage.setItem('postsUpdated', Date.now().toString()); // Notify other components
      }} />}

      {/* Today’s Number Fact */}
      <section className="bg-indigo-50 p-4 rounded mb-6">
        <h3 className="font-semibold">Today’s Number Fact</h3>
        <p>{fact}</p>
      </section>

      {/* Recent Posts Section */}
     

      <section className="space-y-6">
        <h2 className="text-lg font-semibold mb-4 text-gray-900 dark:text-gray-100">Recent Community Posts</h2>
        {posts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {posts.map((post) => (
              <div key={post.id} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
                <h4 className="text-xl font-bold mb-2">{post.title}</h4>
                <div
                  className="text-gray-700 mb-4 prose prose-sm max-w-none"
                  dangerouslySetInnerHTML={{
                    __html: post.content.length > 150 ? post.content.slice(0, 150) + "..." : post.content
                  }}
                />

                {post.image_url && (
                  <img 
                    src={`post.image_url}?t=${new Date().getTime()}`} 
                    alt={post.title} 
                    className="w-full h-48 object-cover rounded mb-4"
                    onError={(e) => {
                      e.target.style.display = 'none';
                    }}
                  />
                )}

                <img 
  src={post.image_url || "https://placehold.co/600x400?text=No+Image"}
  alt={post.title}
  className="w-full h-48 object-cover rounded mb-4"
/>

                <small className="text-sm text-gray-500">
                  {new Date(post.created_at).toLocaleString()}
                
                </small>

                

                <div className="mt-4 flex gap-2">
                  <a
                    href={`/posts/${post.id}`}
                    className="text-indigo-600 hover:underline"
                  >
                    View
                  </a>
                  <ShareButton
                    post={post}
                    className="text-green-600 hover:underline"
                  >
                    📤 Share
                  </ShareButton>
                  <button
                    id={`edit-post-${post.id}`}
                    onClick={() => navigate(`/posts/${post.id}/edit`)}
                    aria-label={`Edit ${post.title}`}
                    className="text-blue-600 hover:underline"
                  >
                    🖋️ Edit
                  </button>
                  <button
                    id={`delete-post-${post.id}`}
                    onClick={() => handleDelete(post.id)}
                    aria-label={`Delete ${post.title}`}
                    className="text-red-600 hover:underline"
                  >
                    🗑️ Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="italic text-gray-500">No posts yet. Be the first to share something meaningful!</p>
        )}
      </section>
    </div>
  );
}
              

      

      
  
