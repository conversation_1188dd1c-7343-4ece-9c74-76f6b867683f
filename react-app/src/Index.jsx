import React, { useState, useEffect } from 'react';
import EnhancedPostCard from './EnhancedPostCard';

export default function Index({ user }) {
  const [posts, setPosts] = useState([]);
  const [fact, setFact] = useState("Loading fact...");

  // Function to fetch posts
  const fetchPosts = () => {
    fetch('/api/posts')
      .then(res => res.json())
      .then(setPosts)
      .catch(err => console.error('Error fetching posts:', err));
  };

  // Fetch recent posts on mount
  useEffect(() => {
    fetchPosts();

    fetch('https://numbersapi.com/random/trivia')
      .then(res => res.text())
      .then(setFact)
      .catch(err => {
        console.error("Failed to load number fact", err);
        setFact("There's always something new to learn.");
      });
  }, []);

  // Listen for post updates from other components
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'postsUpdated') {
        fetchPosts();
        localStorage.removeItem('postsUpdated'); // Clean up
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also check for updates when the page becomes visible
    const handleVisibilityChange = () => {
      if (!document.hidden && localStorage.getItem('postsUpdated')) {
        fetchPosts();
        localStorage.removeItem('postsUpdated');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <section className="py-16 px-4 text-center bg-gradient-to-b from-indigo-50 to-white">
        <h1 className="text-3xl md:text-4xl lg:text-5xl font-extrabold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600">
          Real Honest Conversations for Visionary Minds
        </h1>
        <p className="text-lg max-w-2xl mx-auto mb-8 text-gray-800">
          A platform for scholars, entrepreneurs, and community leaders to share authentic insights, mentor others, and build a better world without the noise of likes or dislikes.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {!user ? (
            <>
              <a href="/login" className="px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105">
                Join Be Real, Get Real
              </a>
              <a href="#features" className="px-6 py-3 rounded-lg border border-gray-300 hover:bg-gray-100 font-medium transition">
                Learn More
              </a>
            </>
          ) : (
            <a href="/dashboard" className="px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition">
              Go to Dashboard
            </a>
          )}
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 px-4 bg-gray-100">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-2xl font-bold text-center mb-10">Core Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
              <h4 className="font-semibold text-lg mb-2">WebRTC Chat</h4>
              <p className="text-gray-600">Secure peer-to-peer video/audio communication between mentors and mentees.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
              <h4 className="font-semibold text-lg mb-2">Offline Capabilities</h4>
              <p className="text-gray-600">Built as a Progressive Web App (PWA) — use it anywhere, even without internet access.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
              <h4 className="font-semibold text-lg mb-2">No Popularity Metrics</h4>
              <p className="text-gray-600">Focus on authentic content and meaningful discussions — no likes, no fake stories, no popularity contests.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
              <h4 className="font-semibold text-lg mb-2">Community Building</h4>
              <p className="text-gray-600">Create and join topic-based communities focused on research, education, or social impact.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Recent Posts Section */}
      <section className="py-16 px-4 text-center">
        <h3 className="text-2xl font-bold mb-8">Recent Community Insights</h3>

        {posts.length > 0 ? (
          <div className="max-w-4xl mx-auto space-y-6">
            {posts.map((post) => (
              <EnhancedPostCard
                key={post.id}
                post={post}
                user={user}
              />
            ))}
          </div>
        ) : (
          <p className="italic text-gray-500">No posts yet. Be the first to share something meaningful!</p>
        )}
      </section>
    </div>
  );
}
            
      
