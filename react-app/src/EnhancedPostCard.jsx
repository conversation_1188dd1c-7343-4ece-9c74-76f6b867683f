import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import CommentPreview from './CommentPreview';
import ShareModal from './ShareModal';

export default function EnhancedPostCard({ post, user }) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [stats, setStats] = useState({
    comments_count: 0,
    user_saved: false
  });
  const [loading, setLoading] = useState(false);
  const [showCommentForm, setShowCommentForm] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [commentLoading, setCommentLoading] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const navigate = useNavigate();

  // Fetch post stats on component mount
  useEffect(() => {
    fetchPostStats();
  }, [post.id]);

  const fetchPostStats = async () => {
    try {
      const response = await fetch(`/api/posts/${post.id}/stats`, {
        credentials: 'include'
      });
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Error fetching post stats:', error);
    }
  };



  const handleSave = async () => {
    if (!user) {
      alert('Please log in to save posts');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/posts/${post.id}/save`, {
        method: 'POST',
        credentials: 'include'
      });

      if (response.ok) {
        const data = await response.json();
        setStats(prev => ({
          ...prev,
          user_saved: data.saved
        }));
        alert(data.saved ? 'Post saved!' : 'Post unsaved!');
      }
    } catch (error) {
      console.error('Error toggling save:', error);
      alert('Failed to toggle save');
    } finally {
      setLoading(false);
    }
  };

  const handleShare = () => {
    setShowShareModal(true);
  };

  const handleComment = () => {
    if (!user) {
      alert('Please log in to comment');
      return;
    }
    setShowCommentForm(!showCommentForm);
  };

  const handleSubmitComment = async (e) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    setCommentLoading(true);
    try {
      const response = await fetch(`/api/comments/${post.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ content: newComment })
      });

      if (response.ok) {
        setNewComment('');
        setShowCommentForm(false);
        // Refresh stats to update comment count
        fetchPostStats();
        // Trigger a refresh of the comment preview
        window.location.reload(); // Simple refresh for now
      } else {
        const errorData = await response.json().catch(() => ({ detail: 'Failed to post comment' }));
        alert(`Failed to post comment: ${errorData.detail || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error posting comment:', error);
      alert('Failed to post comment');
    } finally {
      setCommentLoading(false);
    }
  };

  // Process content for display
  const plainTextContent = post.content.replace(/<[^>]*>/g, '');
  const shouldTruncate = plainTextContent.length > 200;
  const displayContent = isExpanded ? post.content : (plainTextContent.length > 200 ? post.content.slice(0, 200) + '...' : post.content);

  return (
    <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
      {/* Post Header */}
      <div className="flex items-center mb-4">
        <div className="w-10 h-10 bg-indigo-500 rounded-full flex items-center justify-center text-white font-semibold">
          {post.author_username?.charAt(0).toUpperCase() || 'U'}
        </div>
        <div className="ml-3">
          <h5 className="font-semibold text-gray-900">{post.author_username || 'Unknown'}</h5>
          <p className="text-sm text-gray-500">{new Date(post.created_at).toLocaleDateString()}</p>
        </div>
      </div>

      {/* Post Title */}
      <h4 className="text-xl font-bold mb-3 text-gray-900">{post.title}</h4>

      {/* Post Image */}
      {post.image_url && (
        <img
          src={post.image_url}
          alt={post.title}
          className="w-full max-w-md mx-auto rounded-lg mb-4 cursor-pointer"
          onClick={() => navigate(`/posts/${post.id}`)}
        />
      )}

      {/* Post Content */}
      <div className="mb-4">
        <div
          className="text-gray-700 leading-relaxed prose prose-sm max-w-none"
          dangerouslySetInnerHTML={{ __html: displayContent }}
        />

        {shouldTruncate && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-indigo-600 hover:text-indigo-800 text-sm font-medium mt-2"
          >
            {isExpanded ? 'Show Less' : 'Read More'}
          </button>
        )}
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-100">
        {/* Left side - Comment, Share */}
        <div className="flex items-center space-x-6">
          {/* Comment Button */}
          <button
            onClick={handleComment}
            className="flex items-center space-x-1 text-gray-500 hover:text-blue-500 transition-colors"
          >
            <span className="text-lg">💬</span>
            <span className="text-sm font-medium">{stats.comments_count}</span>
          </button>

          {/* Share Button */}
          <button
            onClick={handleShare}
            className="flex items-center space-x-1 text-gray-500 hover:text-green-500 transition-colors"
          >
            <span className="text-lg">📤</span>
            <span className="text-sm font-medium">Share</span>
          </button>
        </div>

        {/* Right side - Save, View */}
        <div className="flex items-center space-x-4">
          {/* Save Button */}
          <button
            onClick={handleSave}
            disabled={loading}
            className={`transition-colors ${
              stats.user_saved 
                ? 'text-yellow-500 hover:text-yellow-600' 
                : 'text-gray-500 hover:text-yellow-500'
            } ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <span className="text-lg">
              {stats.user_saved ? '🔖' : '📑'}
            </span>
          </button>

          {/* View Full Post Button */}
          <button
            onClick={() => navigate(`/posts/${post.id}`)}
            className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
          >
            View Full Post →
          </button>
        </div>
      </div>

      {/* Quick Comment Form */}
      {showCommentForm && user && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <form onSubmit={handleSubmitComment}>
            <textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder="Write a comment..."
              className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              rows="3"
              disabled={commentLoading}
            />
            <div className="flex justify-end space-x-2 mt-2">
              <button
                type="button"
                onClick={() => setShowCommentForm(false)}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
                disabled={commentLoading}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={commentLoading || !newComment.trim()}
                className="px-4 py-2 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {commentLoading ? 'Posting...' : 'Post Comment'}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Comments Preview */}
      <CommentPreview postId={post.id} maxComments={2} />

      {/* Share Modal */}
      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        post={post}
      />
    </div>
  );
}
