import React, { useState } from 'react';

export default function ImageUpload({ onUpload }) {
  const [dragActive, setDragActive] = useState(false);
  const [preview, setPreview] = useState(null);

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      const file = files[0];
      const validTypes = ['image/jpeg', 'image/png', 'image/webp'];

      if (!validTypes.includes(file.type)) {
        alert("Only JPEG, PNG, and WebP images are allowed");
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        setPreview(e.target.result);
        onUpload(file);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleChange = (e) => {
    const file = e.target.files[0];
    const validTypes = ['image/jpeg', 'image/png', 'image/webp'];

    if (!file || !validTypes.includes(file.type)) {
      alert("Only JPEG, PNG, and WebP images are allowed");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target.result);
      onUpload(file);
    };
    reader.readAsDataURL(file);
  };

  return (
    <div
      className={`border-2 border-dashed p-6 rounded-lg text-center cursor-pointer mb-4 transition-colors ${
        dragActive
          ? "bg-blue-50 dark:bg-blue-900 border-blue-500"
          : "border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
      }`}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      <input
        type="file"
        accept="image/*"
        onChange={handleChange}
        className="hidden"
        id="image-upload"
      />
      <label htmlFor="image-upload" className="cursor-pointer">
        <p>Drag & drop an image here, or click to select</p>
        {preview && (
          <img
            src={preview}
            alt="Preview"
            className="mt-4 mx-auto max-h-48"
            loading="lazy"
            decoding="async"
          />
        )}
      </label>
    </div>
  );
}