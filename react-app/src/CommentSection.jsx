import React, { useEffect, useState } from 'react';

export default function CommentSection({ postId }) {
  const [comments, setComments] = useState([]);
  const [newComment, setNewComment] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);

  const loadComments = async (pageNum) => {
    const res = await fetch(`/api/comments/${postId}?page=${pageNum}`);
    const data = await res.json();
    if (data.length === 0) setHasMore(false);
    setComments(prev => [...prev, ...data]);
  };

  useEffect(() => {
    loadComments(page);
  }, [page]);

  const handleLoadMore = () => {
    if (hasMore && !loading) {
      setPage(p => p + 1);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const res = await fetch(`/api/comments/${postId}`, {
        method: "POST",
        body: JSON.stringify({ content: newComment }),
        headers: { "Content-Type": "application/json" },
        credentials: "include"
      });

      if (res.ok) {
        const data = await res.json();
        setComments([data, ...comments]);
        setNewComment('');
      } else {
        const errorData = await res.json().catch(() => ({ detail: 'Failed to submit comment' }));
        alert(`Failed to submit comment: ${errorData.detail || 'Unknown error'}`);
      }
    } catch (err) {
      console.error('Error submitting comment:', err);
      alert("Failed to submit comment: Network error");
    }
  };

  return (
    <section className="mt-8">
      <h3 className="text-lg font-semibold mb-4">Comments</h3>

      {/* List of comments */}
      <div className="space-y-4 mb-6">
        {comments.map((c) => (
          <div key={c.id} className="bg-gray-50 p-4 rounded border">
            <p className="text-gray-800">{c.content}</p>
            <small className="text-sm text-gray-500">
              — {c.author} • {new Date(c.created_at).toLocaleString()}
            </small>
          </div>
        ))}
        {hasMore && (
          <button
            onClick={handleLoadMore}
            className="text-indigo-600 hover:text-indigo-800 underline text-sm"
          >
            Load More Comments
          </button>
        )}
      </div>

      {/* Submit new comment */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add a comment..."
          className="w-full px-4 py-2 border border-gray-300 rounded"
          required
        />
        <button
          type="submit"
          className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded"
        >
          Submit
        </button>
      </form>
    </section>
  );
}