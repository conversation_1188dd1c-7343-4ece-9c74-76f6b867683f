import React, { useState } from 'react';
import ShareModal from './ShareModal';

export default function ShareButton({ post, className = "", children }) {
  const [showShareModal, setShowShareModal] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowShareModal(true)}
        className={className}
      >
        {children || (
          <>
            <span className="text-lg">📤</span>
            <span className="text-sm font-medium">Share</span>
          </>
        )}
      </button>

      <ShareModal 
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        post={post}
      />
    </>
  );
}
