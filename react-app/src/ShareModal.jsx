import React, { useState } from 'react';
import { generateShareUrl, generateShareText, copyToClipboard, generateQRCode, socialShareUrls } from './utils/shareUtils';

export default function ShareModal({ isOpen, onClose, post }) {
  const [copied, setCopied] = useState(false);

  if (!isOpen || !post) return null;

  const shareUrl = generateShareUrl(post.id);
  const shareTitle = post.title;
  const shareText = generateShareText(post);

  const handleCopyLink = async () => {
    const success = await copyToClipboard(shareUrl);
    if (success) {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } else {
      alert('Failed to copy link');
    }
  };

  const shareOptions = [
    {
      name: 'WhatsApp',
      icon: '💬',
      color: 'bg-green-500 hover:bg-green-600',
      url: socialShareUrls.whatsapp(shareTitle, shareText, shareUrl)
    },
    {
      name: 'Email',
      icon: '📧',
      color: 'bg-blue-500 hover:bg-blue-600',
      url: socialShareUrls.email(shareTitle, shareText, shareUrl)
    },
    {
      name: 'Twitter',
      icon: '🐦',
      color: 'bg-sky-500 hover:bg-sky-600',
      url: socialShareUrls.twitter(shareTitle, shareText, shareUrl)
    },
    {
      name: 'Facebook',
      icon: '📘',
      color: 'bg-blue-600 hover:bg-blue-700',
      url: socialShareUrls.facebook(shareTitle, shareText, shareUrl)
    },
    {
      name: 'LinkedIn',
      icon: '💼',
      color: 'bg-blue-700 hover:bg-blue-800',
      url: socialShareUrls.linkedin(shareTitle, shareText, shareUrl)
    },
    {
      name: 'Telegram',
      icon: '✈️',
      color: 'bg-blue-400 hover:bg-blue-500',
      url: socialShareUrls.telegram(shareTitle, shareText, shareUrl)
    },
    {
      name: 'Reddit',
      icon: '🤖',
      color: 'bg-orange-500 hover:bg-orange-600',
      url: socialShareUrls.reddit(shareTitle, shareText, shareUrl)
    },
    {
      name: 'Pinterest',
      icon: '📌',
      color: 'bg-red-500 hover:bg-red-600',
      url: socialShareUrls.pinterest(shareTitle, shareText, shareUrl)
    }
  ];

  const handleShare = (url) => {
    window.open(url, '_blank', 'width=600,height=400');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-semibold text-gray-900">Share Post</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-xl"
          >
            ✕
          </button>
        </div>

        {/* Post Preview */}
        <div className="p-4 border-b bg-gray-50">
          <h4 className="font-medium text-gray-900 mb-1">{shareTitle}</h4>
          <p className="text-sm text-gray-600 line-clamp-2">{shareText}</p>
        </div>

        {/* Copy Link Section */}
        <div className="p-4 border-b">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Copy Link
          </label>
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={shareUrl}
              readOnly
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50"
            />
            <button
              onClick={handleCopyLink}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                copied 
                  ? 'bg-green-100 text-green-700' 
                  : 'bg-indigo-600 text-white hover:bg-indigo-700'
              }`}
            >
              {copied ? '✓ Copied!' : 'Copy'}
            </button>
          </div>
        </div>

        {/* Share Options */}
        <div className="p-4">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Share via
          </label>
          <div className="grid grid-cols-2 gap-3">
            {shareOptions.map((option) => (
              <button
                key={option.name}
                onClick={() => handleShare(option.url)}
                className={`flex items-center space-x-3 p-3 rounded-lg text-white transition-colors ${option.color}`}
              >
                <span className="text-lg">{option.icon}</span>
                <span className="font-medium">{option.name}</span>
              </button>
            ))}
          </div>
        </div>

        {/* QR Code Section */}
        <div className="p-4 border-t bg-gray-50">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            QR Code
          </label>
          <div className="flex justify-center">
            <img
              src={generateQRCode(shareUrl, 120)}
              alt="QR Code"
              className="w-24 h-24 border rounded"
            />
          </div>
          <p className="text-xs text-gray-500 text-center mt-2">
            Scan to open post
          </p>
        </div>
      </div>
    </div>
  );
}
