// Utility functions for sharing posts

export const generateShareUrl = (postId) => {
  return `${window.location.origin}/posts/${postId}`;
};

export const generateShareText = (post) => {
  const cleanContent = post.content.replace(/<[^>]*>/g, '').slice(0, 100);
  return `${post.title}\n\n${cleanContent}${cleanContent.length >= 100 ? '...' : ''}`;
};

export const shareViaWebAPI = async (post) => {
  const shareData = {
    title: post.title,
    text: generateShareText(post),
    url: generateShareUrl(post.id)
  };

  try {
    if (navigator.share) {
      await navigator.share(shareData);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error sharing via Web API:', error);
    return false;
  }
};

export const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    return false;
  }
};

export const generateQRCode = (url, size = 120) => {
  return `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(url)}`;
};

// Social media sharing URLs
export const socialShareUrls = {
  whatsapp: (title, text, url) => 
    `https://wa.me/?text=${encodeURIComponent(`${title}\n\n${text}\n\n${url}`)}`,
  
  email: (title, text, url) => 
    `mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(`${text}\n\nRead more: ${url}`)}`,
  
  twitter: (title, text, url) => 
    `https://twitter.com/intent/tweet?text=${encodeURIComponent(`${title}\n\n${text}`)}&url=${encodeURIComponent(url)}`,
  
  facebook: (title, text, url) => 
    `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
  
  linkedin: (title, text, url) => 
    `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
  
  telegram: (title, text, url) => 
    `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(`${title}\n\n${text}`)}`,
  
  reddit: (title, text, url) => 
    `https://reddit.com/submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`,
  
  pinterest: (title, text, url) => 
    `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(url)}&description=${encodeURIComponent(`${title}\n\n${text}`)}`
};
