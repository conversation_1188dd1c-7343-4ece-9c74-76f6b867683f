// Social interaction functionality for posts
class SocialInteractions {
    constructor() {
        this.init();
    }

    init() {
        this.bindSaveButtons();
        this.bindShareButtons();
        this.bindCommentButtons();
    }

    // Handle save/unsave functionality
    bindSaveButtons() {
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.save-btn') || e.target.closest('.save-btn')) {
                e.preventDefault();
                const button = e.target.matches('.save-btn') ? e.target : e.target.closest('.save-btn');
                const postId = button.dataset.postId;
                
                if (!postId) return;
                
                try {
                    button.disabled = true;
                    const response = await fetch(`/api/posts/${postId}/save`, {
                        method: 'POST',
                        credentials: 'include',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    if (!response.ok) {
                        throw new Error('Failed to toggle save');
                    }

                    const data = await response.json();
                    this.updateSaveButton(button, data.saved);
                    
                    // Show feedback
                    this.showFeedback(data.saved ? 'Post saved!' : 'Post unsaved!');
                    
                } catch (error) {
                    console.error('Save error:', error);
                    this.showFeedback('Failed to save post', 'error');
                } finally {
                    button.disabled = false;
                }
            }
        });
    }

    // Update save button appearance
    updateSaveButton(button, isSaved) {
        const icon = button.querySelector('.save-icon');
        const text = button.querySelector('.save-text');
        
        if (isSaved) {
            button.classList.add('saved');
            button.classList.remove('text-gray-600', 'hover:text-indigo-600');
            button.classList.add('text-indigo-600');
            if (icon) icon.textContent = '🔖';
            if (text) text.textContent = 'Saved';
        } else {
            button.classList.remove('saved');
            button.classList.remove('text-indigo-600');
            button.classList.add('text-gray-600', 'hover:text-indigo-600');
            if (icon) icon.textContent = '📌';
            if (text) text.textContent = 'Save';
        }
    }

    // Handle share functionality
    bindShareButtons() {
        document.addEventListener('click', async (e) => {
            if (e.target.matches('.share-btn') || e.target.closest('.share-btn')) {
                e.preventDefault();
                const button = e.target.matches('.share-btn') ? e.target : e.target.closest('.share-btn');
                const postId = button.dataset.postId;
                const postTitle = button.dataset.postTitle || 'Check out this post';
                
                const postUrl = `${window.location.origin}/posts/${postId}`;
                
                // Try native sharing first
                if (navigator.share) {
                    try {
                        await navigator.share({
                            title: postTitle,
                            url: postUrl
                        });
                        return;
                    } catch (error) {
                        // Fall back to custom share menu
                    }
                }
                
                // Show custom share menu
                this.showShareMenu(postUrl, postTitle);
            }
        });
    }

    // Show custom share menu
    showShareMenu(url, title) {
        const shareMenu = document.createElement('div');
        shareMenu.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        shareMenu.innerHTML = `
            <div class="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full mx-4">
                <h3 class="text-lg font-semibold mb-4">Share this post</h3>
                <div class="space-y-3">
                    <button class="share-option w-full text-left p-3 hover:bg-gray-100 rounded flex items-center" data-method="whatsapp">
                        <span class="mr-3">📱</span> WhatsApp
                    </button>
                    <button class="share-option w-full text-left p-3 hover:bg-gray-100 rounded flex items-center" data-method="email">
                        <span class="mr-3">📧</span> Email
                    </button>
                    <button class="share-option w-full text-left p-3 hover:bg-gray-100 rounded flex items-center" data-method="copy">
                        <span class="mr-3">📋</span> Copy Link
                    </button>
                </div>
                <button class="close-share mt-4 w-full p-2 bg-gray-200 hover:bg-gray-300 rounded">
                    Close
                </button>
            </div>
        `;

        document.body.appendChild(shareMenu);

        // Handle share options
        shareMenu.addEventListener('click', (e) => {
            if (e.target.matches('.share-option')) {
                const method = e.target.dataset.method;
                this.handleShare(method, url, title);
                document.body.removeChild(shareMenu);
            } else if (e.target.matches('.close-share') || e.target === shareMenu) {
                document.body.removeChild(shareMenu);
            }
        });
    }

    // Handle different share methods
    handleShare(method, url, title) {
        switch (method) {
            case 'whatsapp':
                window.open(`https://wa.me/?text=${encodeURIComponent(title + ' ' + url)}`, '_blank');
                break;
            case 'email':
                window.open(`mailto:?subject=${encodeURIComponent(title)}&body=${encodeURIComponent(url)}`, '_blank');
                break;
            case 'copy':
                navigator.clipboard.writeText(url).then(() => {
                    this.showFeedback('Link copied to clipboard!');
                }).catch(() => {
                    this.showFeedback('Failed to copy link', 'error');
                });
                break;
        }
    }

    // Handle comment button clicks
    bindCommentButtons() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.comment-btn') || e.target.closest('.comment-btn')) {
                e.preventDefault();
                const button = e.target.matches('.comment-btn') ? e.target : e.target.closest('.comment-btn');
                const postId = button.dataset.postId;
                
                if (postId) {
                    window.location.href = `/posts/${postId}#comments`;
                }
            }
        });
    }

    // Show feedback messages
    showFeedback(message, type = 'success') {
        const feedback = document.createElement('div');
        feedback.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'error' ? 'bg-red-500 text-white' : 'bg-green-500 text-white'
        }`;
        feedback.textContent = message;
        
        document.body.appendChild(feedback);
        
        setTimeout(() => {
            feedback.style.opacity = '0';
            feedback.style.transition = 'opacity 0.3s';
            setTimeout(() => {
                if (feedback.parentNode) {
                    document.body.removeChild(feedback);
                }
            }, 300);
        }, 3000);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SocialInteractions();
});
