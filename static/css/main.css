/* realhonest/static/css/main.css */

/* Light mode (default) */
body {
  font-family: system-ui, sans-serif;
  background-color: #f9fafb;
  color: #111827;
  transition: background-color 0.3s ease, color 0.3s ease;
}

a {
  color: #6366f1;
  text-decoration: underline;
  transition: color 0.3s ease;
}

button, input, textarea {
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  border: 1px solid #d1d5db;
  transition: background-color 0.2s ease-in-out, border-color 0.3s ease, color 0.3s ease;
  background-color: #ffffff;
  color: #111827;
}

button:hover {
  background-color: #e0e7ff;
}

/* Dark mode styles */
.dark {
  background-color: #111827;
  color: #f9fafb;
}

.dark body {
  background-color: #111827;
  color: #f9fafb;
}

.dark a {
  color: #818cf8;
}

.dark button, .dark input, .dark textarea {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark button:hover {
  background-color: #4b5563;
}

/* Dark mode for specific elements */
.dark .bg-white {
  background-color: #1f2937 !important;
}

.dark .bg-gray-50 {
  background-color: #111827 !important;
}

.dark .bg-gray-100 {
  background-color: #374151 !important;
}

.dark .text-gray-900 {
  color: #f9fafb !important;
}

.dark .text-gray-800 {
  color: #e5e7eb !important;
}

.dark .text-gray-700 {
  color: #d1d5db !important;
}

.dark .text-gray-600 {
  color: #9ca3af !important;
}

.dark .text-gray-500 {
  color: #6b7280 !important;
}

.dark .border-gray-200 {
  border-color: #374151 !important;
}

.dark .border-gray-300 {
  border-color: #4b5563 !important;
}

/* Dark mode for cards and containers */
.dark .shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
}

.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2) !important;
}

/* Dark mode for forms */
.dark input:focus, .dark textarea:focus {
  border-color: #818cf8;
  box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.1);
}

/* Dark mode for navigation */
.dark nav {
  background-color: #1f2937;
  border-color: #374151;
}

/* Dark mode for buttons with specific colors */
.dark .bg-indigo-600 {
  background-color: #6366f1 !important;
}

.dark .bg-indigo-600:hover {
  background-color: #5b21b6 !important;
}

.dark .bg-green-600 {
  background-color: #059669 !important;
}

.dark .bg-green-600:hover {
  background-color: #047857 !important;
}

.dark .bg-red-600 {
  background-color: #dc2626 !important;
}

.dark .bg-red-600:hover {
  background-color: #b91c1c !important;
}

/* Dark mode for hover states */
.dark .hover\\:bg-gray-100:hover {
  background-color: #374151 !important;
}

.dark .hover\\:bg-indigo-100:hover {
  background-color: #312e81 !important;
}

/* Dark mode for gradients */
.dark .bg-gradient-to-r {
  background: linear-gradient(to right, #4338ca, #7c3aed) !important;
}