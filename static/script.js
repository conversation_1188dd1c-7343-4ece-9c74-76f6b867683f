// static/script.js

// ====== Progressive Web App (PWA) Installation Prompt ======
let deferredPrompt = null;

window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault();
    deferredPrompt = e;
});

function addToHomeScreen() {
    if (deferredPrompt) {
        deferredPrompt.prompt();
        deferredPrompt.userChoice.then((choiceResult) => {
            if (choiceResult.outcome === 'accepted') {
                console.log('User accepted the install prompt');
            }
            deferredPrompt = null;
        });
    } else {
        alert("App is already installed or not supported.");
    }
}

// ====== Service Worker Registration ======
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js')
            .then(registration => {
                console.log('Service Worker registered with scope:', registration.scope);
            })
            .catch(error => {
                console.error('Service Worker registration failed:', error);
            });
    });
}

// ====== Dark Mode Toggle ======
async function toggleDarkMode() {
    const isDark = document.documentElement.classList.contains("dark");
    const newDarkMode = !isDark;

    // Update DOM immediately for responsive UI
    if (newDarkMode) {
        document.documentElement.classList.add("dark");
    } else {
        document.documentElement.classList.remove("dark");
    }

    // Update localStorage
    localStorage.setItem("darkMode", newDarkMode ? "true" : "false");

    // Update button text
    updateDarkModeButton(newDarkMode);

    // Save to backend if user is logged in
    try {
        await fetch('/api/user/dark-mode', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({ dark_mode: newDarkMode })
        });
    } catch (error) {
        console.log('Could not save dark mode preference to server:', error);
    }
}

function updateDarkModeButton(isDark) {
    const toggleBtn = document.getElementById("darkModeToggle");
    if (toggleBtn) {
        toggleBtn.textContent = isDark ? "🌞 Light Mode" : "🌙 Dark Mode";
        toggleBtn.setAttribute("aria-label", isDark ? "Switch to light mode" : "Switch to dark mode");
    }
}

async function initializeDarkMode() {
    let darkMode = false;

    // Try to get preference from server first (for logged-in users)
    try {
        const response = await fetch('/api/user/dark-mode', {
            credentials: 'include'
        });
        if (response.ok) {
            const data = await response.json();
            darkMode = data.dark_mode;
        } else {
            // Fall back to localStorage
            const localDarkMode = localStorage.getItem("darkMode");
            darkMode = localDarkMode === "true" ||
                      (localDarkMode === null && window.matchMedia('(prefers-color-scheme: dark)').matches);
        }
    } catch (error) {
        // Fall back to localStorage
        const localDarkMode = localStorage.getItem("darkMode");
        darkMode = localDarkMode === "true" ||
                  (localDarkMode === null && window.matchMedia('(prefers-color-scheme: dark)').matches);
    }

    // Apply dark mode
    if (darkMode) {
        document.documentElement.classList.add("dark");
    } else {
        document.documentElement.classList.remove("dark");
    }

    // Update localStorage to match
    localStorage.setItem("darkMode", darkMode ? "true" : "false");

    // Update button
    updateDarkModeButton(darkMode);
}

document.addEventListener("DOMContentLoaded", async () => {
    // Initialize dark mode
    await initializeDarkMode();

    // Set up dark mode button
    const toggleBtn = document.getElementById("darkModeToggle");
    if (toggleBtn) {
        toggleBtn.addEventListener("click", toggleDarkMode);
    }

    // Conditional Post Button Display
    const contextType = "{{ context.type }}";
    const newPostBtn = document.getElementById("togglePostFormBtn");
    if (newPostBtn) {
        newPostBtn.style.display = contextType === "dashboard" ? "inline-block" : "none";
    }

    // Form Submission Handling
    const form = document.querySelector("form");
    if (form && form.action.includes("/create-profile")) {
        form.addEventListener("submit", async function(e) {
            e.preventDefault();

            const token = getAuthToken();

            try {
                const response = await fetch(form.action, {
                    method: "POST",
                    body: new FormData(form),
                    headers: {
                        "Authorization": token
                    }
                });

                if (response.ok) {
                    window.location.href = "/dashboard";
                } else {
                    const text = await response.text();
                    console.error("Form submission failed:", text);
                    alert("Failed to submit form.");
                }
            } catch (err) {
                console.error("Network error:", err);
                alert("Network error. Please try again.");
            }
        });
    }

    // Token Expiration Check
    const token = "{{ request.cookies.access_token }}";
    if (token && token.startsWith("Bearer ")) {
        const rawToken = token.slice(7);
        if (isTokenExpired(rawToken)) {
            window.location.href = "/login?expired=true";
        }
    }
});

// ====== Utility Functions ======

/**
 * Gets current auth token from cookies
 * @returns {string}
 */
function getAuthToken() {
    return document.cookie.replace(/(?:(?:^|.*;\s*)access_token\s*=\s*([^;]+).*$)|^$/, "$1") || "Bearer";
}

/**
 * Checks if a JWT token is expired
 * @param {string} token - JWT string without "Bearer "
 */
function isTokenExpired(token) {
    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        return payload.exp * 1000 < Date.now();
    } catch (e) {
        return true;
    }
}

/**
 * Gets a cookie by name
 * @param {string} name - Cookie name
 * @returns {string|null}
 */
function getCookie(name) {
    return document.cookie.split("; ").find(row => row.startsWith(name + "="))?.split("=")[1] || null;
}

const CACHE_NAME = "realhonest-static-cache-v5";

self.addEventListener("install", event => {
    event.waitUntil(
        caches.open(CACHE_NAME).then(cache => {
            return cache.addAll([
                "/",
                "/static/main.css",
                "/static/script.js",
                "/static/icon-192.png",
                "/static/icon-512.png"
            ]);
        })
    );
    self.skipWaiting();
});

self.addEventListener("activate", event => {
    const currentCaches = [CACHE_NAME];
    event.waitUntil(
        caches.keys().then(keys => {
            return keys.filter(key => !currentCaches.includes(key));
        }).then(keysToDelete => {
            return Promise.all(keysToDelete.map(key => caches.delete(key)));
        }).then(() => {
            return self.clients.claim();
        })
    );
});

self.addEventListener("fetch", event => {
    if (event.request.method !== "GET") return;

    event.respondWith(
        caches.match(event.request).then(response => {
            return response || fetch(event.request).then(fetchResponse => {
                return caches.open(CACHE_NAME).then(cache => {
                    cache.put(event.request, fetchResponse.clone());
                    return fetchResponse;
                });
            });
        })
    );
});

  
