#!/usr/bin/env python3
"""
Add dark_mode column to users table
"""

import sqlite3
import os

def add_dark_mode_column():
    """Add the dark_mode column to the users table"""
    
    # Connect to the database
    db_path = "realhonest.db"
    if not os.path.exists(db_path):
        print(f"Database {db_path} not found!")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if dark_mode column already exists
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'dark_mode' in columns:
            print("✅ dark_mode column already exists")
            return
        
        # Add dark_mode column
        cursor.execute("""
            ALTER TABLE users 
            ADD COLUMN dark_mode BOOLEAN DEFAULT FALSE
        """)
        
        conn.commit()
        print("✅ dark_mode column added successfully!")
        
        # Verify column was added
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        if 'dark_mode' in columns:
            print("✅ Verified: dark_mode column exists in users table")
        else:
            print("❌ Error: dark_mode column was not added")
        
    except Exception as e:
        print(f"❌ Error adding dark_mode column: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    add_dark_mode_column()
