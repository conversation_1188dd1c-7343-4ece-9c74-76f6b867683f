#!/usr/bin/env python3
"""
Performance testing script for RealHonest
"""

import requests
import time
import statistics

def test_page_load_time(url, num_tests=5):
    """Test page load time"""
    times = []
    
    for i in range(num_tests):
        start_time = time.time()
        try:
            response = requests.get(url, timeout=10)
            end_time = time.time()
            
            if response.status_code == 200:
                load_time = (end_time - start_time) * 1000  # Convert to milliseconds
                times.append(load_time)
                print(f"Test {i+1}: {load_time:.2f}ms")
            else:
                print(f"Test {i+1}: Failed with status {response.status_code}")
        except Exception as e:
            print(f"Test {i+1}: Error - {e}")
    
    if times:
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"\nResults for {url}:")
        print(f"Average load time: {avg_time:.2f}ms")
        print(f"Minimum load time: {min_time:.2f}ms")
        print(f"Maximum load time: {max_time:.2f}ms")
        print(f"Tests completed: {len(times)}/{num_tests}")
        
        return avg_time
    else:
        print("No successful tests completed")
        return None

def test_compression(url):
    """Test if compression is working"""
    headers = {
        'Accept-Encoding': 'gzip, deflate'
    }
    
    response = requests.get(url, headers=headers)
    
    print(f"\nCompression test for {url}:")
    print(f"Content-Encoding: {response.headers.get('Content-Encoding', 'None')}")
    print(f"Content-Length: {response.headers.get('Content-Length', 'Not specified')}")
    print(f"Response size: {len(response.content)} bytes")

def test_cache_headers(url):
    """Test cache headers"""
    response = requests.get(url)
    
    print(f"\nCache headers test for {url}:")
    print(f"Cache-Control: {response.headers.get('Cache-Control', 'Not set')}")
    print(f"Expires: {response.headers.get('Expires', 'Not set')}")
    print(f"ETag: {response.headers.get('ETag', 'Not set')}")

def main():
    """Run performance tests"""
    base_url = "http://127.0.0.1:8000"
    
    print("🚀 RealHonest Performance Test Suite")
    print("=" * 50)
    
    # Test main pages
    pages = [
        "/",
        "/dashboard", 
        "/dark-mode-test"
    ]
    
    for page in pages:
        url = base_url + page
        print(f"\n📊 Testing {page}")
        print("-" * 30)
        test_page_load_time(url)
    
    # Test static files
    static_files = [
        "/static/css/main.css",
        "/static/script.js",
        "/static/js/social.js"
    ]
    
    print(f"\n📁 Testing Static Files")
    print("-" * 30)
    for file in static_files:
        url = base_url + file
        test_cache_headers(url)
    
    # Test compression
    print(f"\n🗜️ Testing Compression")
    print("-" * 30)
    test_compression(base_url + "/")
    
    print(f"\n✅ Performance tests completed!")

if __name__ == "__main__":
    main()
