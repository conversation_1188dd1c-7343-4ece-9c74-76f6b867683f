{% extends "base.html" %}
{% block title %}Dashboard – RealHonest{% endblock %}

{% block sidebar %}
<aside class="bg-white p-4 rounded-lg shadow-md sticky top-6">
  <nav class="space-y-2">
    <a href="/" class="inline-block px-3 py-2 bg-indigo-100 text-indigo-700 hover:bg-indigo-200 rounded-md">🏠 Home</a>
    <a href="/dashboard" class="block py-2 px-3 hover:bg-indigo-100 rounded">Dashboard</a>
    <a href="/profile" class="block py-2 px-3 hover:bg-indigo-100 rounded">🧑 Profile</a>
    <a href="/saved-posts" class="block py-2 px-3 hover:bg-indigo-100 rounded">🔖 Saved Posts</a>
    <a href="/chat" class="block py-2 px-3 hover:bg-indigo-100 rounded">💬 Chat</a>
    <a href="/profile"
   class="inline-block mt-4 px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded text-center w-full">
   🛠️ Change Options
</a>
    <a href="/logout" class="block py-2 px-3 hover:bg-red-100 rounded text-red-600">🚪 Logout</a>
  </nav>
</aside>
{% endblock %}

{% if error_message %}
<div class="bg-red-100 text-red-800 p-4 rounded mb-6">
  {{ error_message }}
</div>
{% endif %}

{% block heading %}
<div class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-6 rounded-lg shadow-lg mb-8">
  <h1 class="text-2xl font-extrabold">Welcome back, {{ user.full_name or user.username }}</h1>
  <p class="mt-2 opacity-90">Share insights, connect, and build honest conversations.
     Get Real, Be Real.</p>
</div>
{% endblock %}

{% block content %}
<p class="mb-6 text-gray-800">{{ context.description }}</p>
<pre>User: {{ user }}</pre>
<pre>User Role: {{ user.role }}</pre>
<pre>User Type: {{ user.role.__class__ }}</pre>

<!-- Mentor CTA -->
{% if user.role != "author" and not user.mentor_profile %}
<div class="bg-white p-6 rounded-lg shadow-md mb-8">
  <h3 class="text-xl font-semibold mb-4">Want to Become a Mentor?</h3>
  <p class="mb-4">Help others grow by offering guidance in your field.</p>
  <a href="/register-mentor"
     class="px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105"
     aria-label="Register as a mentor to help others grow">
    🎓 Register as Mentor
  </a>
</div>
{% elif user.mentor_profile %}
<div class="bg-white p-6 rounded-lg shadow-md mb-8">
  <h3 class="text-xl font-semibold mb-4">You Are a Mentor</h3>
  <p class="mb-4">Your profile is active and available at 
    <a href="/mentors/{{ user.mentor_profile.id }}" class="text-indigo-600 underline">your mentor page</a>.
  </p>
  <div class="flex justify-between">
    <a href="/edit-mentor/{{ user.mentor_profile.id }}" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md">Edit Mentor Profile</a>
    <a href="/mentors" class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md">Browse Other Mentors</a>
  </div>
</div>
{% else %}
<p class="italic text-gray-500 mb-6">Only authors can become mentors.</p>
{% endif %}

<!-- New Post Button + Form -->
{% if user and user.role.lower() == "author" %}
<!-- New Post Button + Form -->
<h2 class="text-2xl font-bold mb-4">Welcome back, {{ user.full_name or user.username }}</h2>
<p class="mb-6 text-gray-600">Write something meaningful today.</p>

<button id="togglePostFormBtn" type="button"
        class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white font-medium rounded-md transition mb-6"
        aria-label="Create a new post">
  ✏️ New Post
</button>

{% if user.github_username %}
  <div class="mt-6 bg-white p-4 rounded shadow-sm">
    <h3 class="font-semibold mb-2">GitHub Profile</h3>
    <p>Followers: {{ github.followers }}</p>
    <p>Repositories: {{ github.public_repos }}</p>
    <p><a href="{{ github.profile_url }}" target="_blank">View on GitHub</a></p>
  </div>
{% endif %}

<!-- Hidden Post Creation Form -->
<section id="postFormSection" class="hidden mb-10 bg-white p-6 rounded-lg shadow-md">
  <h3 class="text-lg font-semibold mb-4">Create a New Post</h3>
  <form action="/create-post" method="post" enctype="multipart/form-data">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

    <div class="mb-4">
      <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Post Title</label>
      <input id="title" type="text" name="title" required
             placeholder="Give your post a strong title..."
             class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 outline-none">
    </div>

    <div class="mb-4">
      <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Post Content</label>
      <textarea id="content" name="content" rows="5" required
                placeholder="Write something meaningful..."
                class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 outline-none"></textarea>
               
                </div>

    <div class="mb-4">
      <label for="post_image" class="block text-sm font-medium text-gray-700 mb-2">Add an Image (Optional)</label>
      <input type="file" id="post_image" name="post_image" accept="image/*"
             class="w-full px-3 py-2 border border-gray-300 rounded-md bg-white text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:bg-indigo-600 file:text-white hover:file:bg-indigo-700 transition-all">
    </div>

    <div class="flex justify-end">
      <button type="submit"
              class="px-5 py-2 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-md shadow transition">
        Publish Post
      </button>
    </div>
  </form>
</section>

<!-- Recent Posts Section -->
<section class="space-y-6">
  <div class="bg-indigo-50 p-4 rounded mb-6">
    <h3 class="font-semibold">Today’s Number Fact</h3>
    <p>{{ fact }}</p>
  </div>

  <h3 class="text-lg font-semibold mb-4">Recent Community Posts</h3>

  {% if posts and posts|length > 0 %}
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {% for post in posts %}
    <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
      <h4 class="text-xl font-bold mb-2">{{ post.title }}</h4>
      <p class="text-gray-700 mb-4">{{ post.content[:150] }}{% if post.content|length > 150 %}...{% endif %}</p>

      {% if post.image_url %}
      <img
        src="{{ post.image_url }}"
        alt="{{ post.title }}"
        width="600"
        height="400"
        loading="lazy"
        class="w-full h-48 object-cover rounded mb-4"
      />
      {% endif %}

      <!-- Social interaction buttons -->
      <div class="flex items-center justify-between pt-4 border-t border-gray-200 mb-3">
        <div class="flex items-center space-x-3">
          <!-- Save button -->
          <button class="save-btn flex items-center space-x-1 px-2 py-1 rounded-md transition-colors {{ 'text-indigo-600' if post.user_saved else 'text-gray-600 hover:text-indigo-600' }}"
                  data-post-id="{{ post.id }}">
            <span class="save-icon">{{ '🔖' if post.user_saved else '📌' }}</span>
            <span class="save-text text-xs">{{ 'Saved' if post.user_saved else 'Save' }}</span>
          </button>

          <!-- Comment button -->
          <button class="comment-btn flex items-center space-x-1 px-2 py-1 rounded-md text-gray-600 hover:text-blue-600 transition-colors"
                  data-post-id="{{ post.id }}">
            <span>💬</span>
            <span class="text-xs">{{ post.comments_count or 0 }}</span>
          </button>

          <!-- Share button -->
          <button class="share-btn flex items-center space-x-1 px-2 py-1 rounded-md text-gray-600 hover:text-green-600 transition-colors"
                  data-post-id="{{ post.id }}"
                  data-post-title="{{ post.title }}">
            <span>📤</span>
            <span class="text-xs">Share</span>
          </button>
        </div>

        <!-- Read more link -->
        <a href="/posts/{{ post.id }}" class="text-indigo-600 hover:text-indigo-800 text-xs font-medium">
          Read more →
        </a>
      </div>

      <small class="text-gray-500">{{ post.created_at | datetimeformat }}</small>
    </div>
    {% endfor %}
  </div>
  {% else %}
  <p class="italic text-gray-500">No posts yet. Be the first to share something meaningful!</p>
  {% endif %}
</section>
  {% endif %}
 <!-- JavaScript for toggling form -->
  <script defer>
    document.addEventListener("DOMContentLoaded", function () {
      const btn = document.getElementById("togglePostFormBtn");
      const form = document.getElementById("postFormSection");

      if (btn && form) {
        btn.addEventListener("click", function () {
          form.classList.toggle("hidden");
        });
      }
    });
  </script>

{% endblock %}

  








   
