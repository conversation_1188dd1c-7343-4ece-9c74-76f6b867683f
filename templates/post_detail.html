{% extends "base.html" %}
{% block title %}{{ post.title }} – RealHonest{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="bg-white p-6 rounded shadow mb-6">
    <h1 class="text-3xl font-bold mb-4">{{ post.title }}</h1>
    {% if post.image_url %}
    <img src="{{ post.image_url }}" alt="{{ post.title }}" class="w-full h-auto mb-4 rounded">
    {% endif %}
    <p class="text-gray-700 whitespace-pre-line">{{ post.content }}</p>
    <small class="text-sm text-gray-500 mt-4 block">
      By {{ post.author.username }} • {{ post.created_at.strftime("%B %d, %Y") }}
    </small>

    <!-- Social interaction buttons -->
    {% if user %}
    <div class="flex items-center justify-between pt-4 border-t border-gray-200 mt-4">
      <div class="flex items-center space-x-4">
        <!-- Save button -->
        <button class="save-btn flex items-center space-x-2 px-4 py-2 rounded-md transition-colors {{ 'text-indigo-600 bg-indigo-50' if post.user_saved else 'text-gray-600 hover:text-indigo-600 hover:bg-indigo-50' }}"
                data-post-id="{{ post.id }}">
          <span class="save-icon">{{ '🔖' if post.user_saved else '📌' }}</span>
          <span class="save-text">{{ 'Saved' if post.user_saved else 'Save Post' }}</span>
        </button>

        <!-- Share button -->
        <button class="share-btn flex items-center space-x-2 px-4 py-2 rounded-md text-gray-600 hover:text-green-600 hover:bg-green-50 transition-colors"
                data-post-id="{{ post.id }}"
                data-post-title="{{ post.title }}">
          <span>📤</span>
          <span>Share</span>
        </button>
      </div>
    </div>
    {% endif %}
  </div>

  <!-- Comments Section -->
<section aria-labelledby="comments-title" class="max-w-3xl mx-auto mt-8">
  <h2 id="comments-title" class="text-lg font-semibold mb-4">Comments</h2>
  <div class="space-y-4">
    {% for comment in comments %}
    <div class="bg-gray-50 p-4 rounded border">
      <p>{{ comment.content }}</p>
      <small class="text-sm text-gray-500">
        — {{ comment.author }} • {{ comment.created_at.strftime("%B %d, %Y") }}
      </small>
    </div>
    {% endfor %}
    {% if not comments %}
    <p class="italic text-gray-500">No comments yet.</p>
    {% endif %}
  </div>
</section>


  <div class="flex gap-4">
    <a href="/posts/{{ post.id }}/edit" class="inline-block px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
      🖋️ Edit
    </a>
    <button onclick="window.location.href='/api/posts/{{ post.id }}'" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded">
      🗑️ Delete
    </button>
  </div>
</div>
{% endblock %}