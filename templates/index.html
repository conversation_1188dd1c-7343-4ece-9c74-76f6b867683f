{% extends "base.html" %}
{% block title %}RealHonest – Honest Conversations for Visionaries{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="py-16 px-4 text-center">
  <h1 class="text-3xl md:text-4xl lg:text-5xl font-extrabold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-purple-600">
    Real Honest Conversations for Visionary Minds
  </h1>
  <p class="text-lg max-w-2xl mx-auto mb-8 text-gray-800">
    A platform for scholars, entrepreneurs, and community leaders to share authentic insights, mentor others, and build a better world without the noise of likes or dislikes.
  </p>
  
  <div class="flex flex-col sm:flex-row gap-4 justify-center">
    {% if not user %}
    <a href="/signup" class="px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105">
      Join <PERSON>, Get Real
    </a>
    <a href="#features" class="px-6 py-3 rounded-lg border border-gray-300 hover:bg-gray-100 font-medium transition">
      Learn More
    </a>
    {% else %}
    <a href="/dashboard" class="px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition">
      Go to Dashboard
    </a>
    {% endif %}
  </div>
</section>

<!-- Features Section -->
<section id="features" class="py-16 px-4 bg-gray-100">
  <div class="max-w-4xl mx-auto">
    <h2 class="text-2xl font-bold text-center mb-10">Core Features</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
        <h4 class="font-semibold text-lg mb-2">WebRTC Chat</h4>
        <p class="text-gray-600">Secure peer-to-peer video/audio communication between mentors and mentees.</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
        <h4 class="font-semibold text-lg mb-2">Offline Capabilities</h4>
        <p class="text-gray-600">Built as a Progressive Web App (PWA) — use it anywhere, even without internet access.</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
        <h4 class="font-semibold text-lg mb-2">No Likes/Dislikes</h4>
        <p class="text-gray-600">Focus on substance over popularity — no fake posts, no algorithm manipulation.</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
        <h4 class="font-semibold text-lg mb-2">Community Building</h4>
        <p class="text-gray-600">Create and join topic-based communities focused on research, education, or social impact.</p>
      </div>
    </div>
  </div>
</section>

<!-- Recent Posts Section -->
<section class="py-16 px-4 text-center">
  <h3 class="text-2xl font-bold mb-8">Recent Community Insights</h3>

  {% if posts and posts|length > 0 %}
  <div class="max-w-4xl mx-auto space-y-6">
    {% for post in posts %}
    <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
      <h4 class="text-xl font-bold mb-2">{{ post.title }}</h4>
      {% if post.image_url %}
      <img
        src="{{ post.image_url }}"
        alt="{{ post.title }}"
        width="600"
        height="400"
        loading="lazy"
        class="w-full rounded mb-4"
      />
      {% endif %}
      <p class="text-gray-700 mb-4">{{ post.content[:150] }}{% if post.content|length > 150 %}...{% endif %}</p>

      <!-- Social interaction buttons -->
      {% if user %}
      <div class="flex items-center justify-between pt-4 border-t border-gray-200">
        <div class="flex items-center space-x-4">
          <!-- Save button -->
          <button class="save-btn flex items-center space-x-1 px-3 py-2 rounded-md transition-colors {{ 'text-indigo-600' if post.user_saved else 'text-gray-600 hover:text-indigo-600' }}"
                  data-post-id="{{ post.id }}">
            <span class="save-icon">{{ '🔖' if post.user_saved else '📌' }}</span>
            <span class="save-text text-sm">{{ 'Saved' if post.user_saved else 'Save' }}</span>
          </button>

          <!-- Comment button -->
          <button class="comment-btn flex items-center space-x-1 px-3 py-2 rounded-md text-gray-600 hover:text-blue-600 transition-colors"
                  data-post-id="{{ post.id }}">
            <span>💬</span>
            <span class="text-sm">{{ post.comments_count or 0 }} Comments</span>
          </button>

          <!-- Share button -->
          <button class="share-btn flex items-center space-x-1 px-3 py-2 rounded-md text-gray-600 hover:text-green-600 transition-colors"
                  data-post-id="{{ post.id }}"
                  data-post-title="{{ post.title }}">
            <span>📤</span>
            <span class="text-sm">Share</span>
          </button>
        </div>

        <!-- Read more link -->
        <a href="/posts/{{ post.id }}" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
          Read more →
        </a>
      </div>
      {% endif %}

      <div class="text-sm text-gray-500 mt-3">
        By {{ post.author.username if post.author else 'Anonymous' }} • {{ post.created_at | datetimeformat }}
      </div>
    </div>
    {% endfor %}
  </div>
  {% else %}
  <p class="text-gray-500 italic">No posts yet. Be the first to share something meaningful!</p>
  {% endif %}
</section>

<!-- Call to Action -->
<section class="py-16 px-4 text-center">
  {% if user and not user.mentor_profile %}
  <h3 class="text-2xl font-bold mb-4">Become a Mentor</h3>
  <p class="text-gray-800 max-w-xl mx-auto mb-6">
    You're logged in as <strong>{{ user.username }}</strong>. Share your expertise by becoming a mentor.
  </p>
  <div class="flex flex-col sm:flex-row justify-center gap-4">
    <a href="/register-mentor" 
   class="px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition transform hover:scale-105"
   aria-label="Register as a mentor to help others grow">
  🎓 Register as Mentor
</a>
  </div>
  {% elif user %}
  <h3 class="text-2xl font-bold mb-4">You are a Mentor</h3>
  <p class="text-gray-800 max-w-xl mx-auto mb-6">
    You’re registered as a mentor. Your profile is live and visible to learners.
  </p>
  <div class="flex flex-col sm:flex-row justify-center gap-4">
    <a href="/mentors/{{ user.mentor_profile.id }}" class="px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition">
      View My Mentor Profile
    </a>
  </div>
  {% else %}
  <h3 class="text-2xl font-bold mb-4">Join the Open-minded</h3>
  <p class="text-gray-800 max-w-xl mx-auto mb-6">
    Login or sign up to start sharing meaningful ideas and connect with mentors.
  </p>
  <div class="flex flex-col sm:flex-row justify-center gap-4">
    <a href="/signup" class="px-6 py-3 rounded-lg bg-indigo-600 hover:bg-indigo-700 text-white font-medium transition">
      Sign Up
    </a>
    <a href="/login" class="px-6 py-3 rounded-lg border border-gray-300 hover:bg-gray-100 font-medium transition">
      Login
    </a>
  </div>
  {% endif %}
</section>
{% endblock %}