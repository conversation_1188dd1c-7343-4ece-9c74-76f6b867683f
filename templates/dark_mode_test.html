{% extends "base.html" %}
{% block title %}Dark Mode Test – RealHonest{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
  <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-6 transition-colors duration-300">
    <h1 class="text-3xl font-bold mb-4 text-gray-900 dark:text-gray-100">Dark Mode Test Page</h1>
    <p class="text-gray-700 dark:text-gray-300 mb-4">
      This page is designed to test the dark mode functionality. Toggle the dark mode button in the navigation to see the changes.
    </p>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <!-- Card 1 -->
      <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors duration-300">
        <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Sample Card</h3>
        <p class="text-gray-600 dark:text-gray-400">This is a sample card to test dark mode styling.</p>
        <button class="mt-3 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md transition-colors">
          Sample Button
        </button>
      </div>
      
      <!-- Card 2 -->
      <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg transition-colors duration-300">
        <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Form Elements</h3>
        <input type="text" placeholder="Sample input" 
               class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-colors">
        <textarea placeholder="Sample textarea" 
                  class="w-full mt-2 p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-colors"></textarea>
      </div>
    </div>
    
    <!-- Links and Text Colors -->
    <div class="mb-6">
      <h3 class="text-lg font-semibold mb-2 text-gray-900 dark:text-gray-100">Links and Text</h3>
      <p class="text-gray-700 dark:text-gray-300 mb-2">
        This is regular text. <a href="#" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300">This is a link</a>.
      </p>
      <p class="text-gray-500 dark:text-gray-400">This is muted text.</p>
    </div>
    
    <!-- Status Messages -->
    <div class="space-y-3">
      <div class="p-3 bg-green-100 dark:bg-green-900 border border-green-300 dark:border-green-700 rounded-md">
        <p class="text-green-800 dark:text-green-200">Success message</p>
      </div>
      <div class="p-3 bg-yellow-100 dark:bg-yellow-900 border border-yellow-300 dark:border-yellow-700 rounded-md">
        <p class="text-yellow-800 dark:text-yellow-200">Warning message</p>
      </div>
      <div class="p-3 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-md">
        <p class="text-red-800 dark:text-red-200">Error message</p>
      </div>
    </div>
  </div>
  
  <!-- Dark Mode Status -->
  <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md transition-colors duration-300">
    <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Dark Mode Status</h2>
    <div id="darkModeStatus" class="text-gray-700 dark:text-gray-300">
      Checking dark mode status...
    </div>
    <button onclick="testDarkModeAPI()" 
            class="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
      Test Dark Mode API
    </button>
  </div>
</div>

<script>
// Test dark mode functionality
function updateDarkModeStatus() {
  const isDark = document.documentElement.classList.contains('dark');
  const localStorageValue = localStorage.getItem('darkMode');
  const statusEl = document.getElementById('darkModeStatus');
  
  statusEl.innerHTML = `
    <p><strong>Current Mode:</strong> ${isDark ? 'Dark' : 'Light'}</p>
    <p><strong>LocalStorage:</strong> ${localStorageValue}</p>
    <p><strong>System Preference:</strong> ${window.matchMedia('(prefers-color-scheme: dark)').matches ? 'Dark' : 'Light'}</p>
  `;
}

async function testDarkModeAPI() {
  try {
    const response = await fetch('/api/user/dark-mode', {
      credentials: 'include'
    });
    const data = await response.json();
    
    const statusEl = document.getElementById('darkModeStatus');
    statusEl.innerHTML += `<p><strong>Server Preference:</strong> ${data.dark_mode ? 'Dark' : 'Light'}</p>`;
  } catch (error) {
    console.error('Error testing dark mode API:', error);
    const statusEl = document.getElementById('darkModeStatus');
    statusEl.innerHTML += `<p><strong>API Error:</strong> ${error.message}</p>`;
  }
}

// Update status on page load and when dark mode changes
document.addEventListener('DOMContentLoaded', updateDarkModeStatus);

// Listen for dark mode changes
const observer = new MutationObserver(updateDarkModeStatus);
observer.observe(document.documentElement, {
  attributes: true,
  attributeFilter: ['class']
});
</script>
{% endblock %}
