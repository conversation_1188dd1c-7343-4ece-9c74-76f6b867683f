{% extends "base.html" %}
{% block title %}Saved Posts – RealHonest{% endblock %}

{% block sidebar %}
<aside class="bg-white p-4 rounded-lg shadow-md sticky top-6">
  <nav class="space-y-2">
    <a href="/" class="inline-block px-3 py-2 bg-indigo-100 text-indigo-700 hover:bg-indigo-200 rounded-md">🏠 Home</a>
    <a href="/dashboard" class="block py-2 px-3 hover:bg-indigo-100 rounded">Dashboard</a>
    <a href="/profile" class="block py-2 px-3 hover:bg-indigo-100 rounded">🧑 Profile</a>
    <a href="/saved-posts" class="block py-2 px-3 bg-indigo-100 text-indigo-700 rounded">🔖 Saved Posts</a>
    <a href="/chat" class="block py-2 px-3 hover:bg-indigo-100 rounded">💬 Chat</a>
    <a href="/logout" class="block py-2 px-3 hover:bg-red-100 rounded text-red-600">🚪 Logout</a>
  </nav>
</aside>
{% endblock %}

{% block heading %}
<div class="bg-gradient-to-r from-indigo-500 to-purple-600 text-white p-6 rounded-lg shadow-lg mb-8">
  <h1 class="text-2xl font-extrabold">Your Saved Posts</h1>
  <p class="mt-2 opacity-90">Posts you've saved for later reading</p>
</div>
{% endblock %}

{% block content %}
<div id="saved-posts-container" class="space-y-6">
  <div class="text-center py-8">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
    <p class="mt-2 text-gray-600">Loading your saved posts...</p>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', async () => {
  const container = document.getElementById('saved-posts-container');
  
  try {
    const response = await fetch('/api/saved-posts', {
      credentials: 'include'
    });
    
    if (!response.ok) {
      throw new Error('Failed to load saved posts');
    }
    
    const savedPosts = await response.json();
    
    if (savedPosts.length === 0) {
      container.innerHTML = `
        <div class="text-center py-12">
          <div class="text-6xl mb-4">📌</div>
          <h3 class="text-xl font-semibold text-gray-700 mb-2">No saved posts yet</h3>
          <p class="text-gray-500 mb-6">Start saving posts you want to read later!</p>
          <a href="/dashboard" class="inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition">
            Browse Posts
          </a>
        </div>
      `;
      return;
    }
    
    container.innerHTML = savedPosts.map(post => `
      <div class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition">
        <div class="flex justify-between items-start mb-4">
          <h3 class="text-xl font-bold text-gray-900">${post.title}</h3>
          <button class="save-btn text-indigo-600 hover:text-red-600 transition-colors" 
                  data-post-id="${post.id}" 
                  title="Remove from saved">
            <span class="save-icon">🔖</span>
          </button>
        </div>
        
        ${post.image_url ? `
          <img src="${post.image_url}" 
               alt="${post.title}" 
               class="w-full h-48 object-cover rounded mb-4">
        ` : ''}
        
        <p class="text-gray-700 mb-4">${post.content.length > 200 ? post.content.substring(0, 200) + '...' : post.content}</p>
        
        <div class="flex justify-between items-center pt-4 border-t border-gray-200">
          <div class="text-sm text-gray-500">
            By ${post.author_username} • Saved ${new Date(post.saved_at).toLocaleDateString()}
          </div>
          <a href="/posts/${post.id}" 
             class="text-indigo-600 hover:text-indigo-800 font-medium">
            Read full post →
          </a>
        </div>
      </div>
    `).join('');
    
  } catch (error) {
    console.error('Error loading saved posts:', error);
    container.innerHTML = `
      <div class="text-center py-12">
        <div class="text-6xl mb-4">❌</div>
        <h3 class="text-xl font-semibold text-gray-700 mb-2">Failed to load saved posts</h3>
        <p class="text-gray-500 mb-6">Please try refreshing the page</p>
        <button onclick="location.reload()" 
                class="inline-block px-6 py-3 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition">
          Refresh
        </button>
      </div>
    `;
  }
});
</script>
{% endblock %}
