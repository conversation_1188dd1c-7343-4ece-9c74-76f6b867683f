<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>{% block title %}RealHonest – get real, be real{% endblock %}</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="{{ context.description }}" />
  

  <!-- Page Title -->
  <title>{{ context.title }}</title>

  <!-- Favicon -->
  <link rel="icon" href="/static/favicon.ico" type="image/x-icon" />

  <link rel="stylesheet" href="/static/css/main.css">
  <!-- Manifest & Theme Color -->
  <link rel="manifest" href="/static/manifest.json" />
  <meta name="theme-color" content="#C20000" />

  

  <!-- Open Graph / Social Sharing -->
  {% if context.type in ['dashboard', 'profile', 'mentor_profile'] %}
  <meta property="og:title" content="{{ context.title }}" />
  <meta property="og:description" content="{{ context.description }}" />
  <meta property="og:image" content="/static/icon-512.png" />
  <meta property="og:url" content="https://yourdomain.com{{  request.url.path }}" />
  <meta property="og:type" content="website" />
  {% endif %}

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:site" content="@yourhandle" />
  <meta name="twitter:title" content="{{ context.title }}" />
  <meta name="twitter:description" content="{{ context.description }}" />
  <meta name="twitter:image" content="/static/icon-512.png" />

  <!-- Load Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      darkMode: 'class',
      theme: {
        extend: {}
      }
    }
  </script>
  <!-- Preload critical assets -->
  <link rel="preload" as="script" href="/static/script.js">
  <link rel="preload" as="image" href="/static/icon-512.png">
  <!-- Apple Touch Icon -->
  <link rel="apple-touch-icon" href="/static/icon-192.png" />
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 min-h-screen flex flex-col font-sans transition-colors duration-300">

  <!-- Skip to Content Link -->
  <a href="#main-content" class="sr-only focus:not([class~="\sr-only\"]) fixed top-0 left-0 m-4 p-3 bg-indigo-600 text-white rounded z-50">
    Skip to content
  </a>

  <!-- Header -->
  <header class="bg-white dark:bg-gray-800 shadow-sm p-4 flex justify-between items-center transition-colors duration-300">
    <div class="flex items-center space-x-2">
      <svg class="w-6 h-6 text-indigo-600 dark:text-indigo-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
      </svg>
      <h1 class="text-xl font-bold text-gray-900 dark:text-gray-100">RealHonest – get real, be real</h1>
    </div>

    <!-- Navigation -->
    <nav class="space-x-4 hidden md:flex text-gray-700 dark:text-gray-300">
      <a href="/" class="hover:text-indigo-600 dark:hover:text-indigo-400 transition">🏠 Home</a>
      <a href="/dashboard" class="hover:text-indigo-600 dark:hover:text-indigo-400 transition">Dashboard</a>
      <a href="/mentors" class="hover:text-indigo-600 dark:hover:text-indigo-400 transition">Mentors</a>

      {% if user %}
      <a href="/profile" class="hover:text-indigo-600 dark:hover:text-indigo-400 transition">🧑 Profile</a>
      <button id="darkModeToggle" aria-label="Toggle dark mode"
              class="focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400 rounded-md px-3 py-1 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
        🌙 Dark Mode
      </button>
      <a href="/logout" class="hover:text-red-600 dark:hover:text-red-400 transition">🚪 Logout</a>
      {% else %}
      <a href="/login" class="hover:text-indigo-600 dark:hover:text-indigo-400 transition">Login</a>
      <a href="/signup" class="hover:text-indigo-600 dark:hover:text-indigo-400 transition">Sign Up</a>
      {% endif %}
    </nav>
  </header>

  <!-- Main Layout -->
  <div class="flex-grow container mx-auto p-6 flex flex-col md:flex-row gap-6">

    <!-- Sidebar Block -->
    <aside class="md:w-64 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sticky top-6 transition-colors duration-300">
      {% block sidebar %}
      <nav class="space-y-2">
        <a href="/" class="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">🏠 Home</a>
        <a href="/dashboard" class="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">Dashboard</a>
        <a href="/profile" class="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">Profile</a>
        <a href="/chat" class="block py-2 px-3 hover:bg-indigo-100 dark:hover:bg-indigo-900 rounded text-gray-700 dark:text-gray-300 transition-colors">Chat</a>
        <a href="/logout" class="block py-2 px-3 hover:bg-red-100 dark:hover:bg-red-900 rounded text-red-600 dark:text-red-400 transition-colors">Logout</a>
      </nav>
      {% endblock %}
    </aside>

    <!-- Main Content Area -->
    <main role="main" id="main-content" class="flex-grow">
      {% block heading %}{% endblock %}
      {% block content %}{% endblock %}
    </main>


  </div>

  <!-- Footer -->
  <footer class="py-6 px-4 text-center text-gray-500 dark:text-gray-400 text-sm bg-white dark:bg-gray-800 border-t dark:border-gray-700 mt-auto transition-colors duration-300">
    
    &copy; {{ now.year }} RealHonest - get real, be real. All rights reserved.
  </footer>

  <!-- Scripts -->
  <script src="/static/script.js"></script>

  <!-- Service Worker Registration -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js')
          .then(registration => console.log('Service Worker registered:', registration.scope))
          .catch(error => console.error('Service Worker registration failed:', error));
      });
    }

    document.addEventListener("DOMContentLoaded", function () {
      const btn = document.getElementById("togglePostFormBtn");
      const form = document.getElementById("postFormSection");

      if (btn && form) {
        btn.addEventListener("click", function () {
          form.classList.toggle("hidden");
        });
      }
    });

  </script>

  <!-- Social interactions script -->
  <script src="/static/js/social.js"></script>
</body>
</html>