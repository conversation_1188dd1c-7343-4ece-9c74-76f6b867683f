<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>{% block title %}RealHonest – get real, be real{% endblock %}</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="{{ context.description }}" />
  

  <!-- Page Title -->
  <title>{{ context.title }}</title>

  <!-- Favicon -->
  <link rel="icon" href="/static/favicon.ico" type="image/x-icon" />

  <link rel="stylesheet" href="/static/css/main.css">
  <!-- Manifest & Theme Color -->
  <link rel="manifest" href="/static/manifest.json" />
  <meta name="theme-color" content="#C20000" />

  

  <!-- Open Graph / Social Sharing -->
  {% if context.type in ['dashboard', 'profile', 'mentor_profile'] %}
  <meta property="og:title" content="{{ context.title }}" />
  <meta property="og:description" content="{{ context.description }}" />
  <meta property="og:image" content="/static/icon-512.png" />
  <meta property="og:url" content="https://yourdomain.com{{  request.url.path }}" />
  <meta property="og:type" content="website" />
  {% endif %}

  <!-- Twitter Card -->
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:site" content="@yourhandle" />
  <meta name="twitter:title" content="{{ context.title }}" />
  <meta name="twitter:description" content="{{ context.description }}" />
  <meta name="twitter:image" content="/static/icon-512.png" />

  <!-- Load Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- Preload critical assets -->
  <link rel="preload" as="script" href="/static/script.js">
  <link rel="preload" as="image" href="/static/icon-512.png">
  <!-- Apple Touch Icon -->
  <link rel="apple-touch-icon" href="/static/icon-192.png" />
</head>
<body class="bg-gray-50 text-gray-900 min-h-screen flex flex-col font-sans">

  <!-- Skip to Content Link -->
  <a href="#main-content" class="sr-only focus:not([class~="\sr-only\"]) fixed top-0 left-0 m-4 p-3 bg-indigo-600 text-white rounded z-50">
    Skip to content
  </a>

  <!-- Header -->
  <header class="bg-white shadow-sm p-4 flex justify-between items-center">
    <div class="flex items-center space-x-2">
      <svg class="w-6 h-6 text-indigo-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
      </svg>
      <h1 class="text-xl font-bold">RealHonest – get real, be real</h1>
    </div>

    <!-- Navigation -->
    <nav class="space-x-4 hidden md:flex">
      <a href="/" class="hover:text-indigo-600 transition">🏠 Home</a>
      <a href="/dashboard" class="hover:text-indigo-600 transition">Dashboard</a>
      <a href="/mentors" class="hover:text-indigo-600 transition">Mentors</a>

      {% if user %}
      <a href="/profile" class="hover:text-indigo-600 transition">🧑 Profile</a>
      <button id="darkModeToggle" aria-label="Toggle dark mode"
              class="focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded-md px-3 py-1">
        🌙 Toggle Dark Mode
      </button>
      <a href="/logout" class="hover:text-red-600 transition">🚪 Logout</a>
      {% else %}
      <a href="/login" class="hover:text-indigo-600 transition">Login</a>
      <a href="/signup" class="hover:text-indigo-600 transition">Sign Up</a>
      {% endif %}
    </nav>
  </header>

  <!-- Main Layout -->
  <div class="flex-grow container mx-auto p-6 flex flex-col md:flex-row gap-6">
    
    <!-- Sidebar Block -->
    <aside class="md:w-64 bg-white rounded-lg shadow-md p-4 sticky top-6">
      {% block sidebar %}
      <nav class="space-y-2">
        <a href="/" class="block py-2 px-3 hover:bg-indigo-100 rounded">🏠 Home</a>
        <a href="/dashboard" class="block py-2 px-3 hover:bg-indigo-100 rounded">Dashboard</a>
        <a href="/profile" class="block py-2 px-3 hover:bg-indigo-100 rounded">Profile</a>
        <a href="/chat" class="block py-2 px-3 hover:bg-indigo-100 rounded">Chat</a>
        <a href="/logout" class="block py-2 px-3 hover:bg-red-100 rounded text-red-600">Logout</a>
      </nav>
      {% endblock %}
    </aside>

    <!-- Main Content Area -->
    <main role="main" id="main-content" class="flex-grow">
      {% block heading %}{% endblock %}
      {% block content %}{% endblock %}
    </main>
   

  </div>

  <!-- Footer -->
  <footer class="py-6 px-4 text-center text-gray-500 text-sm bg-white border-t mt-auto">
    
    &copy; {{ now.year }} RealHonest - get real, be real. All rights reserved.
  </footer>

  <!-- Scripts -->
  <script src="/static/script.js"></script>

  <!-- Service Worker Registration -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js')
          .then(registration => console.log('Service Worker registered:', registration.scope))
          .catch(error => console.error('Service Worker registration failed:', error));
      });
    }

    document.addEventListener("DOMContentLoaded", function () {
      const btn = document.getElementById("togglePostFormBtn");
      const form = document.getElementById("postFormSection");

      if (btn && form) {
        btn.addEventListener("click", function () {
          form.classList.toggle("hidden");
        });
      }
    });

  </script>

  <!-- Social interactions script -->
  <script src="/static/js/social.js"></script>
</body>
</html>